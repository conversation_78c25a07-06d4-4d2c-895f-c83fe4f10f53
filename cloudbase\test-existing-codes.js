#!/usr/bin/env node

// 测试现有的会员码
const axios = require('axios');

async function testExistingCodes() {
  console.log('🔍 测试现有会员码...');
  
  const testCodes = ['VIP001', 'VIP002', 'VIP123'];
  
  for (const code of testCodes) {
    console.log(`\n测试会员码: ${code}`);
    try {
      const response = await axios.post('https://api.dznovel.top/api/member-code/validate', {
        code: code
      });
      console.log(`   ✅ ${code}: ${response.data.message}`);
    } catch (error) {
      if (error.response) {
        console.log(`   ❌ ${code}: ${error.response.data.message}`);
      } else {
        console.log(`   ❌ ${code}: ${error.message}`);
      }
    }
  }
}

// 运行测试
testExistingCodes();
