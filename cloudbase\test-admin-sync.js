#!/usr/bin/env node

// 测试管理员访问数据同步记录API
const axios = require('axios');

async function testAdminSyncAPI() {
  console.log('🔍 测试管理员数据同步API...');
  
  try {
    // 1. 先登录管理员获取token
    console.log('\n1. 管理员登录:');
    const loginResponse = await axios.post('https://api.dznovel.top/api/admin/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    if (loginResponse.data.success) {
      const adminToken = loginResponse.data.data.token;
      console.log('   ✅ 管理员登录成功');
      console.log('   Token:', adminToken.substring(0, 50) + '...');
      
      // 2. 测试获取同步记录
      console.log('\n2. 获取数据同步记录:');
      try {
        const syncResponse = await axios.get('https://api.dznovel.top/api/sync/records?page=1&size=10&_api_path=sync/records', {
          headers: {
            'Authorization': `Bearer ${adminToken}`
          }
        });
        
        if (syncResponse.data.success) {
          console.log('   ✅ 获取同步记录成功');
          console.log('   记录总数:', syncResponse.data.data.total);
          console.log('   当前页记录数:', syncResponse.data.data.syncRecords.length);
          
          if (syncResponse.data.data.syncRecords.length > 0) {
            const firstRecord = syncResponse.data.data.syncRecords[0];
            console.log('   第一条记录:');
            console.log('     - ID:', firstRecord.id);
            console.log('     - 用户ID:', firstRecord.userId);
            console.log('     - 用户名:', firstRecord.username);
            console.log('     - 同步时间:', firstRecord.syncTime);
            console.log('     - 同步内容:', firstRecord.syncContent);
            console.log('     - 同步方式:', firstRecord.syncMethod);
            console.log('     - 数据大小:', firstRecord.dataSize, '字节');
          }
        } else {
          console.log('   ❌ 获取同步记录失败:', syncResponse.data.message);
        }
      } catch (syncError) {
        if (syncError.response) {
          console.log('   ❌ API错误:', syncError.response.status, syncError.response.data);
        } else {
          console.log('   ❌ 网络错误:', syncError.message);
        }
      }
      
      // 3. 测试获取同步统计
      console.log('\n3. 获取同步统计:');
      try {
        const statsResponse = await axios.get('https://api.dznovel.top/api/sync/stats?_api_path=sync/stats', {
          headers: {
            'Authorization': `Bearer ${adminToken}`
          }
        });
        
        if (statsResponse.data.success) {
          console.log('   ✅ 获取统计成功');
          console.log('   统计数据:', statsResponse.data.data);
        } else {
          console.log('   ❌ 获取统计失败:', statsResponse.data.message);
        }
      } catch (statsError) {
        if (statsError.response) {
          console.log('   ❌ 统计API错误:', statsError.response.status, statsError.response.data);
        } else {
          console.log('   ❌ 统计网络错误:', statsError.message);
        }
      }
      
    } else {
      console.log('   ❌ 管理员登录失败:', loginResponse.data.message);
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error.message);
  }
}

// 运行测试
testAdminSyncAPI();
