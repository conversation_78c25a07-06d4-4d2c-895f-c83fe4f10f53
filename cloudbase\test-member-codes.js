#!/usr/bin/env node

// 测试CloudBase会员码数据
const axios = require('axios');

async function testMemberCodeAPI() {
  console.log('🔍 测试会员码API...');
  
  try {
    // 1. 测试验证一个不存在的会员码
    console.log('\n1. 测试不存在的会员码:');
    try {
      const response1 = await axios.post('https://api.dznovel.top/api/member-code/validate', {
        code: 'NONEXISTENT123'
      });
      console.log('   响应:', response1.data);
    } catch (error) {
      if (error.response) {
        console.log('   响应:', error.response.data);
      } else {
        console.log('   错误:', error.message);
      }
    }

    // 2. 测试创建一个会员码
    console.log('\n2. 创建测试会员码:');
    try {
      const response2 = await axios.post('https://api.dznovel.top/api/admin/member-code/create', {
        packageId: 'pkg_permanent',
        customCode: 'TEST' + Date.now(),
        expireAt: null,
        batchId: 'test_batch'
      });
      console.log('   创建结果:', response2.data);
      
      if (response2.data.success) {
        const testCode = response2.data.data.code;
        console.log('   创建的会员码:', testCode);
        
        // 3. 验证刚创建的会员码
        console.log('\n3. 验证刚创建的会员码:');
        try {
          const response3 = await axios.post('https://api.dznovel.top/api/member-code/validate', {
            code: testCode
          });
          console.log('   验证结果:', response3.data);
        } catch (error) {
          if (error.response) {
            console.log('   验证失败:', error.response.data);
          } else {
            console.log('   验证错误:', error.message);
          }
        }
      }
    } catch (error) {
      if (error.response) {
        console.log('   创建失败:', error.response.data);
      } else {
        console.log('   创建错误:', error.message);
      }
    }

    // 4. 获取会员码列表
    console.log('\n4. 获取会员码列表:');
    try {
      const response4 = await axios.get('https://api.dznovel.top/api/admin/member-code/list?page=1&limit=20');
      console.log('   总数:', response4.data.data.pagination.total);
      console.log('   会员码详情:');
      response4.data.data.codes.forEach((code, index) => {
        console.log(`   ${index + 1}. ${code.code} - ${code.isUsed ? '已使用' : '未使用'} - 套餐:${code.packageId}`);
        if (code.expireAt) {
          console.log(`      过期时间: ${code.expireAt}`);
        }
        if (code.isUsed) {
          console.log(`      使用者: ${code.usedBy}, 使用时间: ${code.usedAt}`);
        }
      });
    } catch (error) {
      if (error.response) {
        console.log('   获取失败:', error.response.data);
      } else {
        console.log('   获取错误:', error.message);
      }
    }

  } catch (error) {
    console.error('测试过程中发生错误:', error.message);
  }
}

// 运行测试
testMemberCodeAPI();
