<template>
  <div class="member-codes-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">会员码管理</h1>
      <p class="page-subtitle">管理和生成会员激活码</p>
    </div>

    <!-- 操作栏 -->
    <div class="dashboard-card">
      <div class="action-bar">
        <div class="action-left">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索会员码"
            style="width: 300px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select
            v-model="statusFilter"
            placeholder="状态筛选"
            style="width: 120px"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="未使用" value="unused" />
            <el-option label="已使用" value="used" />
            <el-option label="已过期" value="expired" />
          </el-select>

          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </div>

        <div class="action-right">
          <el-button type="primary" @click="showCustomDialog">
            <el-icon><EditPen /></el-icon>
            添加自定义会员码
          </el-button>

          <el-button type="success" @click="showGenerateDialog">
            <el-icon><Plus /></el-icon>
            批量生成会员码
          </el-button>

          <el-button
            type="danger"
            :disabled="selectedCodes.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除 ({{ selectedCodes.length }})
          </el-button>
        </div>
      </div>
    </div>

    <!-- 会员码表格 -->
    <div class="dashboard-card">
      <el-table
        v-loading="loading"
        :data="memberCodeList"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="code" label="会员码" width="150">
          <template #default="{ row }">
            <div class="code-cell">
              <code class="member-code">{{ row.code }}</code>
              <el-button
                type="text"
                size="small"
                @click="copyCode(row.code)"
              >
                <el-icon><CopyDocument /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="绑定套餐" width="200">
          <template #default="{ row }">
            <el-tag
              :type="getPackageTagType(row.packageId)"
              size="small"
            >
              {{ getPackageText(row.packageId) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusTagType(row)"
              size="small"
            >
              {{ getStatusText(row) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="usedBy" label="使用者" width="120">
          <template #default="{ row }">
            <span v-if="row.usedBy">
              <el-button type="text" @click="viewUser(row.usedBy)">
                {{ getUserName(row.usedBy) }}
              </el-button>
            </span>
            <span v-else class="unused">未使用</span>
          </template>
        </el-table-column>

        <el-table-column prop="usedAt" label="使用时间" width="160">
          <template #default="{ row }">
            <span v-if="row.usedAt">
              {{ formatDate(row.usedAt) }}
            </span>
            <span v-else class="unused">-</span>
          </template>
        </el-table-column>

        <el-table-column prop="expireAt" label="过期时间" width="160">
          <template #default="{ row }">
            <span v-if="row.expireAt">
              {{ formatDate(row.expireAt) }}
            </span>
            <span v-else class="permanent">永久有效</span>
          </template>
        </el-table-column>

        <el-table-column prop="batchId" label="批次ID" width="180" show-overflow-tooltip />

        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEditCode(row)"
              :disabled="row.isUsed"
            >
              编辑
            </el-button>

            <el-popconfirm
              title="确定要删除这个会员码吗？"
              @confirm="handleDeleteCode(row)"
            >
              <template #reference>
                <el-button
                  type="danger"
                  size="small"
                  :disabled="row.isUsed"
                >
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 添加自定义会员码对话框 -->
    <el-dialog
      v-model="customDialogVisible"
      title="添加自定义会员码"
      width="600px"
      @close="handleCustomDialogClose"
    >
      <el-form
        ref="customFormRef"
        :model="customForm"
        :rules="customRules"
        label-width="100px"
      >
        <el-form-item label="绑定套餐" prop="packageId">
          <el-select v-model="customForm.packageId" style="width: 100%" placeholder="请选择要绑定的套餐">
            <el-option
              v-for="pkg in packageList"
              :key="pkg.id"
              :label="`${pkg.name} (${pkg.price}${pkg.unit})`"
              :value="pkg.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="会员码" prop="codes">
          <el-input
            v-model="customForm.codesInput"
            type="textarea"
            :rows="6"
            placeholder="请输入自定义会员码，每行一个&#10;例如：&#10;MYVIP001&#10;CUSTOM123&#10;SPECIAL999"
            style="width: 100%"
          />
          <div class="input-tip">
            <el-icon><InfoFilled /></el-icon>
            <span>每行输入一个会员码，系统会自动检查重复</span>
          </div>
        </el-form-item>

        <el-form-item label="过期时间" prop="expireAt">
          <el-date-picker
            v-model="customForm.expireAt"
            type="datetime"
            placeholder="选择过期时间（可选）"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="预览">
          <div class="codes-preview">
            <el-tag
              v-for="(code, index) in parsedCodes"
              :key="index"
              :type="code.isValid ? 'success' : 'danger'"
              style="margin: 2px 4px 2px 0;"
            >
              {{ code.value }}
              <span v-if="!code.isValid" class="error-reason">{{ code.error }}</span>
            </el-tag>
            <div v-if="parsedCodes.length === 0" class="no-codes">
              请在上方输入会员码
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="customDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleAddCustom"
          :loading="customLoading"
          :disabled="validCodes.length === 0"
        >
          添加 {{ validCodes.length }} 个会员码
        </el-button>
      </template>
    </el-dialog>

    <!-- 批量生成会员码对话框 -->
    <el-dialog
      v-model="generateDialogVisible"
      title="批量生成会员码"
      width="500px"
      @close="handleGenerateDialogClose"
    >
      <el-form
        ref="generateFormRef"
        :model="generateForm"
        :rules="generateRules"
        label-width="100px"
      >
        <el-form-item label="绑定套餐" prop="packageId">
          <el-select v-model="generateForm.packageId" style="width: 100%" placeholder="请选择要绑定的套餐">
            <el-option
              v-for="pkg in packageList"
              :key="pkg.id"
              :label="`${pkg.name} (${pkg.price}${pkg.unit})`"
              :value="pkg.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="生成数量" prop="count">
          <el-input-number
            v-model="generateForm.count"
            :min="1"
            :max="100"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="前缀" prop="prefix">
          <el-input v-model="generateForm.prefix" placeholder="如: VIP" />
        </el-form-item>

        <el-form-item label="过期时间" prop="expireAt">
          <el-date-picker
            v-model="generateForm.expireAt"
            type="datetime"
            placeholder="选择过期时间（可选）"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="generateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleGenerate" :loading="generateLoading">
          生成
        </el-button>
      </template>
    </el-dialog>

    <!-- 编辑会员码对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑会员码"
      width="500px"
      @close="handleEditDialogClose"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="会员码" prop="code">
          <el-input v-model="editForm.code" placeholder="请输入会员码" />
        </el-form-item>

        <el-form-item label="绑定套餐" prop="packageId">
          <el-select v-model="editForm.packageId" style="width: 100%" placeholder="请选择要绑定的套餐">
            <el-option
              v-for="pkg in packageList"
              :key="pkg.id"
              :label="`${pkg.name} (${pkg.price}${pkg.unit})`"
              :value="pkg.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="过期时间" prop="expireAt">
          <el-date-picker
            v-model="editForm.expireAt"
            type="datetime"
            placeholder="选择过期时间（可选）"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveEdit" :loading="editLoading">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const generateLoading = ref(false)
const customLoading = ref(false)
const editLoading = ref(false)
const generateDialogVisible = ref(false)
const customDialogVisible = ref(false)
const editDialogVisible = ref(false)
const selectedCodes = ref([])
const generateFormRef = ref<FormInstance>()
const customFormRef = ref<FormInstance>()
const editFormRef = ref<FormInstance>()

const searchKeyword = ref('')
const statusFilter = ref('')

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 会员码列表
const memberCodeList = ref([])

// 套餐列表
const packageList = ref([])

// 生成表单
const generateForm = reactive({
  packageId: '',
  count: 10,
  prefix: 'VIP',
  expireAt: null as any
})

// 自定义会员码表单
const customForm = reactive({
  packageId: '',
  codesInput: '',
  expireAt: null as any
})

// 编辑会员码表单
const editForm = reactive({
  id: '',
  code: '',
  packageId: '',
  expireAt: null as any
})

// 表单验证规则
const generateRules: FormRules = {
  packageId: [
    { required: true, message: '请选择会员类型', trigger: 'change' }
  ],
  count: [
    { required: true, message: '请输入生成数量', trigger: 'blur' }
  ],
  prefix: [
    { required: true, message: '请输入前缀', trigger: 'blur' }
  ]
}

const customRules: FormRules = {
  packageId: [
    { required: true, message: '请选择会员类型', trigger: 'change' }
  ],
  codes: [
    { required: true, message: '请输入会员码', trigger: 'blur' }
  ]
}

const editRules: FormRules = {
  code: [
    { required: true, message: '请输入会员码', trigger: 'blur' },
    { min: 3, max: 20, message: '会员码长度应在3-20个字符之间', trigger: 'blur' },
    { pattern: /^[A-Z0-9]+$/, message: '会员码只能包含大写字母和数字', trigger: 'blur' }
  ],
  packageId: [
    { required: true, message: '请选择会员类型', trigger: 'change' }
  ]
}

// 获取套餐标签类型
const getPackageTagType = (packageId: string) => {
  const pkg = packageList.value.find(p => p.id === packageId)
  if (!pkg) return 'info'

  // 根据套餐类型或价格设置不同颜色
  if (pkg.durationDays === -1) return 'success' // 永久套餐
  if (pkg.durationDays <= 30) return 'warning' // 月套餐
  if (pkg.durationDays <= 365) return 'primary' // 年套餐
  return 'info'
}

// 获取套餐文本
const getPackageText = (packageId: string) => {
  const pkg = packageList.value.find(p => p.id === packageId)
  return pkg ? `${pkg.name} (${pkg.price}${pkg.unit})` : '未知套餐'
}

// 获取状态标签类型
const getStatusTagType = (row: any) => {
  if (row.isUsed) return 'success'
  if (row.expireAt && new Date(row.expireAt) < new Date()) return 'danger'
  return 'info'
}

// 获取状态文本
const getStatusText = (row: any) => {
  if (row.isUsed) return '已使用'
  if (row.expireAt && new Date(row.expireAt) < new Date()) return '已过期'
  return '未使用'
}

// 获取用户名
const getUserName = (userId: string) => {
  // 这里应该从用户列表中查找用户名
  return userId.substring(0, 8) + '...'
}

// 格式化日期
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

// 复制会员码
const copyCode = async (code: string) => {
  try {
    await navigator.clipboard.writeText(code)
    ElMessage.success('会员码已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 查看用户
const viewUser = (userId: string) => {
  router.push(`/users/${userId}`)
}

// 处理搜索
const handleSearch = () => {
  pagination.page = 1
  loadMemberCodes()
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedCodes.value = selection
}

// 处理批量删除
const handleBatchDelete = async () => {
  if (selectedCodes.value.length === 0) {
    ElMessage.warning('请选择要删除的会员码')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedCodes.value.length} 个会员码吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const codeValues = selectedCodes.value.map(code => code.code)

    const response = await fetch('/api/member-codes/batch-delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      },
      body: JSON.stringify({ codeIds: codeValues })
    })

    const result = await response.json()

    if (response.ok && result.success) {
      ElMessage.success(result.message)
      selectedCodes.value = []
      loadMemberCodes()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error.message) {
      console.error('批量删除失败:', error)
      ElMessage.error('删除失败')
    }
    // 用户取消操作时不显示错误
  }
}

// 处理编辑会员码
const handleEditCode = (code: any) => {
  editForm.id = code.id
  editForm.code = code.code
  editForm.packageId = code.packageId
  editForm.expireAt = code.expireAt ? new Date(code.expireAt) : null
  editDialogVisible.value = true
}

// 处理保存编辑
const handleSaveEdit = async () => {
  if (!editFormRef.value) return

  try {
    const valid = await editFormRef.value.validate()
    if (!valid) return

    editLoading.value = true

    const response = await fetch(`/api/member-codes/${editForm.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      },
      body: JSON.stringify({
        code: editForm.code,
        packageId: editForm.packageId,
        expireAt: editForm.expireAt ? editForm.expireAt.toISOString() : null
      })
    })

    const result = await response.json()

    if (response.ok && result.success) {
      ElMessage.success(result.message)
      editDialogVisible.value = false
      loadMemberCodes()
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存会员码失败:', error)
    ElMessage.error('保存失败')
  } finally {
    editLoading.value = false
  }
}

// 处理编辑对话框关闭
const handleEditDialogClose = () => {
  editForm.id = ''
  editForm.code = ''
  editForm.packageId = packageList.value.length > 0 ? packageList.value[0].id : ''
  editForm.expireAt = null
  if (editFormRef.value) {
    editFormRef.value.resetFields()
  }
}

// 处理删除会员码
const handleDeleteCode = async (code: any) => {
  try {
    const response = await fetch(`/api/member-codes/${code.code}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    const result = await response.json()

    if (response.ok && result.success) {
      ElMessage.success(result.message)
      loadMemberCodes()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    console.error('删除会员码失败:', error)
    ElMessage.error('删除失败')
  }
}

// 解析会员码输入
const parsedCodes = computed(() => {
  if (!customForm.codesInput.trim()) return []

  const codes = customForm.codesInput
    .split('\n')
    .map(code => code.trim())
    .filter(code => code.length > 0)

  return codes.map(code => {
    const validation = validateMemberCode(code)
    return {
      value: code,
      isValid: validation.isValid,
      error: validation.error
    }
  })
})

// 有效的会员码
const validCodes = computed(() => {
  return parsedCodes.value
    .filter(code => code.isValid)
    .map(code => code.value)
})

// 验证会员码格式
const validateMemberCode = (code: string) => {
  if (!code || code.length < 3) {
    return { isValid: false, error: '长度不足' }
  }

  if (code.length > 20) {
    return { isValid: false, error: '长度过长' }
  }

  if (!/^[A-Z0-9]+$/.test(code)) {
    return { isValid: false, error: '只能包含大写字母和数字' }
  }

  return { isValid: true, error: '' }
}

// 显示自定义会员码对话框
const showCustomDialog = () => {
  customDialogVisible.value = true
}

// 显示生成对话框
const showGenerateDialog = () => {
  generateDialogVisible.value = true
}

// 处理添加自定义会员码
const handleAddCustom = async () => {
  if (!customFormRef.value) return

  if (validCodes.value.length === 0) {
    ElMessage.warning('请输入有效的会员码')
    return
  }

  try {
    customLoading.value = true

    const response = await fetch('/api/member-codes/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      },
      body: JSON.stringify({
        packageId: customForm.packageId,
        customCodes: validCodes.value,
        expireAt: customForm.expireAt ? customForm.expireAt.toISOString() : null
      })
    })

    const result = await response.json()

    if (response.ok && result.success) {
      ElMessage.success(result.message)
      customDialogVisible.value = false
      loadMemberCodes()
    } else {
      ElMessage.error(result.message || '添加失败')
    }
  } catch (error) {
    console.error('添加自定义会员码失败:', error)
    ElMessage.error('添加失败')
  } finally {
    customLoading.value = false
  }
}

// 处理生成
const handleGenerate = async () => {
  if (!generateFormRef.value) return
  
  try {
    const valid = await generateFormRef.value.validate()
    if (!valid) return
    
    generateLoading.value = true

    const response = await fetch('/api/member-codes/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      },
      body: JSON.stringify({
        packageId: generateForm.packageId,
        count: generateForm.count,
        prefix: generateForm.prefix,
        expireAt: generateForm.expireAt ? generateForm.expireAt.toISOString() : null
      })
    })

    const result = await response.json()

    if (response.ok && result.success) {
      ElMessage.success(result.message)
      generateDialogVisible.value = false
      loadMemberCodes()
    } else {
      ElMessage.error(result.message || '生成失败')
    }
  } catch (error) {
    console.error('生成会员码失败:', error)
    ElMessage.error('生成失败')
  } finally {
    generateLoading.value = false
  }
}

// 处理自定义对话框关闭
const handleCustomDialogClose = () => {
  customForm.codesInput = ''
  customForm.packageId = packageList.value.length > 0 ? packageList.value[0].id : ''
  customForm.expireAt = null
  if (customFormRef.value) {
    customFormRef.value.resetFields()
  }
}

// 处理生成对话框关闭
const handleGenerateDialogClose = () => {
  if (generateFormRef.value) {
    generateFormRef.value.resetFields()
  }
}

// 处理页面大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadMemberCodes()
}

// 处理页面变化
const handlePageChange = (page: number) => {
  pagination.page = page
  loadMemberCodes()
}

// 加载套餐列表
const loadPackages = async () => {
  try {
    const response = await fetch('/api/packages', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        // 只显示激活的套餐用于绑定会员码
        packageList.value = (result.data || []).filter(pkg => pkg.isActive)

        // 如果表单中的packageId为空且有可用套餐，设置默认值
        if (!generateForm.packageId && packageList.value.length > 0) {
          generateForm.packageId = packageList.value[0].id
        }
        if (!customForm.packageId && packageList.value.length > 0) {
          customForm.packageId = packageList.value[0].id
        }
      } else {
        ElMessage.error(result.message || '加载套餐列表失败')
      }
    } else {
      ElMessage.error('网络请求失败')
    }
  } catch (error) {
    console.error('加载套餐列表失败:', error)
    ElMessage.error('加载套餐列表失败')
  }
}

// 加载会员码列表
const loadMemberCodes = async () => {
  loading.value = true
  try {
    const params = new URLSearchParams({
      page: pagination.page.toString(),
      size: pagination.size.toString(),
      keyword: searchKeyword.value,
      status: statusFilter.value
    })

    const response = await fetch(`/api/member-codes?${params}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        memberCodeList.value = result.data.memberCodes || []
        pagination.total = result.data.total || 0
      } else {
        ElMessage.error(result.message || '加载会员码列表失败')
      }
    } else {
      ElMessage.error('网络请求失败')
    }
  } catch (error) {
    console.error('加载会员码列表失败:', error)
    ElMessage.error('加载会员码列表失败')
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(async () => {
  await loadPackages() // 先加载套餐列表
  await loadMemberCodes() // 再加载会员码列表
})
</script>

<style scoped>
.member-codes-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #8c8c8c;
  margin: 0;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.action-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.action-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.code-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.member-code {
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #1890ff;
}

.unused {
  color: #d9d9d9;
}

.permanent {
  color: #52c41a;
  font-weight: 500;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.input-tip {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
  font-size: 12px;
  color: #8c8c8c;
}

.codes-preview {
  max-height: 120px;
  overflow-y: auto;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px;
  background: #fafafa;
}

.codes-preview .el-tag {
  margin: 2px 4px 2px 0;
}

.error-reason {
  font-size: 10px;
  margin-left: 4px;
  opacity: 0.8;
}

.no-codes {
  color: #8c8c8c;
  font-size: 12px;
  text-align: center;
  padding: 20px;
}

/* 响应式 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-left,
  .action-right {
    justify-content: center;
  }
}
</style>
