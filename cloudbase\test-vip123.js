const axios = require('axios');

async function testVIP123() {
  try {
    console.log('测试会员码 VIP123...');
    const response = await axios.post('https://api.dznovel.top/api/member-code/validate', {
      code: 'VIP123'
    });
    console.log('✅ VIP123 验证成功:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('❌ VIP123 验证失败:', error.response.data);
    } else {
      console.log('❌ 网络错误:', error.message);
    }
  }
}

testVIP123();
