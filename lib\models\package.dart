import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'package.g.dart';

/// 会员套餐模型
@HiveType(typeId: 26)
@JsonSerializable()
class MembershipPackage extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name; // 套餐名称

  @HiveField(2)
  final String description; // 套餐描述

  @HiveField(3)
  final double price; // 价格

  @HiveField(4)
  final int durationDays; // 有效期天数，-1表示永久

  @HiveField(5)
  final List<String> features; // 功能特性列表

  @HiveField(6)
  final MembershipLimits limits; // 使用限制

  @HiveField(7)
  final bool isActive; // 是否启用

  @HiveField(8)
  final int sortOrder; // 排序

  @HiveField(9)
  final DateTime createdAt;

  @HiveField(10)
  DateTime updatedAt;

  @HiveField(11)
  final bool displayOnly; // 是否仅显示但不出售

  MembershipPackage({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.durationDays,
    required this.features,
    required this.limits,
    this.isActive = true,
    this.sortOrder = 0,
    this.displayOnly = false,
    required this.createdAt,
    required this.updatedAt,
  });

  /// 是否为永久套餐
  bool get isPermanent => durationDays == -1;

  /// 获取套餐类型描述
  String get typeDescription {
    if (isPermanent) return '永久会员';
    if (durationDays >= 365) return '年会员';
    if (durationDays >= 30) return '月会员';
    return '${durationDays}天会员';
  }

  factory MembershipPackage.fromJson(Map<String, dynamic> json) =>
      _$MembershipPackageFromJson(json);
  Map<String, dynamic> toJson() => _$MembershipPackageToJson(this);
}

/// 会员限制配置
@HiveType(typeId: 27)
@JsonSerializable()
class MembershipLimits extends HiveObject {
  @HiveField(0)
  final int maxChaptersPerNovel; // 每部小说最大章节数，-1表示无限制

  @HiveField(1)
  final int maxKnowledgeDocuments; // 最大知识库文档数

  @HiveField(2)
  final bool canUseExtendedFeatures; // 是否可以使用扩展功能

  @HiveField(3)
  final int maxNovelsPerDay; // 每天最大生成小说数

  @HiveField(4)
  final int maxWordsPerGeneration; // 每次生成最大字数

  @HiveField(5)
  final bool canExportToMultipleFormats; // 是否支持多格式导出

  @HiveField(6)
  final bool canUseAdvancedAI; // 是否可以使用高级AI模型

  @HiveField(7)
  final int maxCustomCharacterTypes; // 最大自定义角色类型数

  MembershipLimits({
    this.maxChaptersPerNovel = -1,
    this.maxKnowledgeDocuments = 20,
    this.canUseExtendedFeatures = true,
    this.maxNovelsPerDay = -1,
    this.maxWordsPerGeneration = -1,
    this.canExportToMultipleFormats = true,
    this.canUseAdvancedAI = true,
    this.maxCustomCharacterTypes = -1,
  });

  factory MembershipLimits.fromJson(Map<String, dynamic> json) =>
      _$MembershipLimitsFromJson(json);
  Map<String, dynamic> toJson() => _$MembershipLimitsToJson(this);
}

/// 免费用户限制
class FreeLimits {
  static final MembershipLimits free = MembershipLimits(
    maxChaptersPerNovel: 100, // 免费用户每日最多100章
    maxKnowledgeDocuments: 5, // 最多5个知识库文档
    canUseExtendedFeatures: false, // 不能使用扩展功能
    maxNovelsPerDay: -1, // 不限制每日生成小说数
    maxWordsPerGeneration: -1, // 不限制每次生成字数
    canExportToMultipleFormats: true, // 可以导出多种格式
    canUseAdvancedAI: true, // 可以使用AI模型（用户自己的API）
    maxCustomCharacterTypes: -1, // 不限制自定义角色类型数
  );
}

/// 会员码模型
@HiveType(typeId: 28)
@JsonSerializable()
class MemberCode extends HiveObject {
  @HiveField(0)
  final String code; // 会员码

  @HiveField(1)
  final String packageId; // 对应的套餐ID

  @HiveField(2)
  final bool isUsed; // 是否已使用

  @HiveField(3)
  final String? usedBy; // 使用者用户ID

  @HiveField(4)
  final DateTime? usedAt; // 使用时间

  @HiveField(5)
  final DateTime createdAt;

  @HiveField(6)
  final DateTime? expireAt; // 会员码过期时间

  @HiveField(7)
  final String? batchId; // 批次ID

  MemberCode({
    required this.code,
    required this.packageId,
    this.isUsed = false,
    this.usedBy,
    this.usedAt,
    required this.createdAt,
    this.expireAt,
    this.batchId,
  });

  /// 检查会员码是否有效
  bool get isValid {
    if (isUsed) return false;
    if (expireAt != null && DateTime.now().isAfter(expireAt!)) return false;
    return true;
  }

  factory MemberCode.fromJson(Map<String, dynamic> json) =>
      _$MemberCodeFromJson(json);
  Map<String, dynamic> toJson() => _$MemberCodeToJson(this);
}
