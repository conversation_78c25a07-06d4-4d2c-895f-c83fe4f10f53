import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../services/payment_service.dart';

/// 会员码使用页面
class MemberCodeScreen extends StatefulWidget {
  const MemberCodeScreen({Key? key}) : super(key: key);

  @override
  State<MemberCodeScreen> createState() => _MemberCodeScreenState();
}

class _MemberCodeScreenState extends State<MemberCodeScreen> {
  final TextEditingController _codeController = TextEditingController();
  final PaymentService _paymentService = Get.find<PaymentService>();

  bool _isValidating = false;
  bool _isUsing = false;
  String? _validationMessage;
  bool _isCodeValid = false;

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  /// 验证会员码
  Future<void> _validateCode() async {
    final code = _codeController.text.trim();
    if (code.isEmpty) {
      setState(() {
        _validationMessage = '请输入会员码';
        _isCodeValid = false;
      });
      return;
    }

    setState(() {
      _isValidating = true;
      _validationMessage = null;
    });

    try {
      final isValid = await _paymentService.validateMemberCode(code);
      setState(() {
        _isCodeValid = isValid;
        _validationMessage = isValid ? '会员码有效！' : '会员码无效或已过期';
      });
    } catch (e) {
      setState(() {
        _isCodeValid = false;
        _validationMessage = '验证失败：${e.toString()}';
      });
    } finally {
      setState(() {
        _isValidating = false;
      });
    }
  }

  /// 使用会员码
  Future<void> _useMemberCode() async {
    final code = _codeController.text.trim();
    if (code.isEmpty || !_isCodeValid) {
      Get.snackbar('错误', '请先验证有效的会员码');
      return;
    }

    setState(() {
      _isUsing = true;
    });

    try {
      final result = await _paymentService.useMemberCode(code);

      if (result != null) {
        Get.snackbar(
          '成功',
          '会员码使用成功！您已获得会员权益',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // 返回上一页
        Get.back();
      }
    } catch (e) {
      Get.snackbar('错误', '使用会员码失败：${e.toString()}');
    } finally {
      setState(() {
        _isUsing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('使用会员码'),
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 说明卡片
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue[700]),
                      const SizedBox(width: 8),
                      Text(
                        '会员码使用说明',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    '• 输入有效的会员码即可获得对应套餐的会员权益\n'
                    '• 每个会员码只能使用一次\n'
                    '• 使用后将立即生效，请确认后再使用',
                    style: TextStyle(fontSize: 14, height: 1.5),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // 会员码输入
            const Text(
              '会员码',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _codeController,
                    decoration: InputDecoration(
                      hintText: '请输入会员码',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: const Icon(Icons.card_giftcard),
                    ),
                    textCapitalization: TextCapitalization.characters,
                    onChanged: (value) {
                      setState(() {
                        _validationMessage = null;
                        _isCodeValid = false;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _isValidating ? null : _validateCode,
                  child: _isValidating
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('验证'),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // 验证结果
            if (_validationMessage != null)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _isCodeValid ? Colors.green[50] : Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _isCodeValid ? Colors.green[200]! : Colors.red[200]!,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _isCodeValid ? Icons.check_circle : Icons.error,
                      color: _isCodeValid ? Colors.green[700] : Colors.red[700],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _validationMessage!,
                        style: TextStyle(
                          color: _isCodeValid
                              ? Colors.green[700]
                              : Colors.red[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            const Spacer(),

            // 使用按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isCodeValid && !_isUsing ? _useMemberCode : null,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isUsing
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 12),
                          Text('使用中...'),
                        ],
                      )
                    : const Text(
                        '使用会员码',
                        style: TextStyle(fontSize: 16),
                      ),
              ),
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
