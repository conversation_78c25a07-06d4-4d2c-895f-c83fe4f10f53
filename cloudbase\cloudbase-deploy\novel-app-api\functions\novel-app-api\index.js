const fs = require('fs');
const path = require('path');
const cloudbase = require('@cloudbase/node-sdk');

// 初始化CloudBase SDK (使用3.x版本的正确API)
const app = cloudbase.init({
  env: process.env.TCB_ENV || 'novel-app-2gywkgnn15cbd6a8'
});

const db = app.database();

// 短信服务配置
const SMS_CONFIG = {
  // 腾讯云短信服务配置
  secretId: process.env.SMS_SECRET_ID || 'your-secret-id',
  secretKey: process.env.SMS_SECRET_KEY || 'your-secret-key',
  region: 'ap-beijing',
  appId: process.env.SMS_APP_ID || 'your-app-id',
  signName: process.env.SMS_SIGN_NAME || '岱宗文脉',
  templateId: process.env.SMS_TEMPLATE_ID || 'your-template-id',
  // 开发环境标志 - 如果没有配置真实的短信服务，则认为是开发环境
  isDevelopment: process.env.NODE_ENV !== 'production' ||
                 process.env.SMS_SECRET_ID === 'your-secret-id' ||
                 !process.env.SMS_SECRET_ID
};

// 验证码存储（生产环境应使用Redis）
const verificationCodes = new Map();

// COS配置已删除 - 统一使用CloudBase数据库存储

// COS签名生成函数已删除 - 不再需要COS直传功能

// COS临时密钥生成函数已删除 - 不再需要COS直传功能

// COS Authorization签名函数已删除 - 不再需要COS直传功能

// 分块存储用户数据的辅助函数
async function storeUserDataInChunks(userId, data, timestamp, chunkInfo) {
  try {
    const dataString = JSON.stringify(data);
    const chunkSize = 1024 * 1024; // 1MB per chunk
    const totalChunks = Math.ceil(dataString.length / chunkSize);

    console.log(`开始分块存储用户数据: ${userId}, 总大小: ${dataString.length} bytes, 分块数: ${totalChunks}`);

    // 清除该用户的旧数据块
    await db.collection('user_data_chunks').where({
      userId: userId
    }).remove();

    // 存储新的数据块
    for (let i = 0; i < totalChunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, dataString.length);
      const chunkData = dataString.substring(start, end);

      const chunkRecord = {
        userId: userId,
        chunkIndex: i,
        totalChunks: totalChunks,
        chunkData: chunkData,
        timestamp: timestamp,
        createdAt: new Date().toISOString(),
        chunkSize: chunkData.length
      };

      await db.collection('user_data_chunks').add(chunkRecord);
    }

    // 记录同步历史
    const syncRecord = {
      userId: userId,
      timestamp: timestamp,
      totalSize: dataString.length,
      totalChunks: totalChunks,
      status: 'completed',
      syncTime: new Date().toISOString(),
      chunkInfo: chunkInfo || null
    };

    await db.collection('sync_records').add(syncRecord);

    console.log(`✅ 用户数据分块存储完成: ${userId}, ${totalChunks} 个块`);
    return true;
  } catch (error) {
    console.error('❌ 分块存储失败:', error);
    return false;
  }
}

// 从分块存储中恢复用户数据的辅助函数
async function retrieveUserDataFromChunks(userId) {
  try {
    console.log(`开始从分块存储恢复用户数据: ${userId}`);

    // 获取用户的所有数据块，按索引排序
    const chunks = await db.collection('user_data_chunks').where({
      userId: userId
    }).orderBy('chunkIndex', 'asc').get();

    if (chunks.data.length === 0) {
      console.log(`用户 ${userId} 没有分块数据`);
      return null;
    }

    // 重组数据
    let dataString = '';
    for (const chunk of chunks.data) {
      dataString += chunk.chunkData;
    }

    const data = JSON.parse(dataString);
    console.log(`✅ 用户数据恢复完成: ${userId}, 总大小: ${dataString.length} bytes`);
    return data;
  } catch (error) {
    console.error('❌ 数据恢复失败:', error);
    return null;
  }
}

// 发送短信验证码
async function sendSMSCode(phoneNumber, code) {
  // 开发环境直接返回成功
  if (SMS_CONFIG.isDevelopment) {
    console.log(`[开发环境] 发送验证码到 ${phoneNumber}: ${code}`);
    return { success: true, message: '开发环境模拟发送成功' };
  }

  try {
    // 生产环境使用腾讯云短信服务
    const tencentcloud = require('tencentcloud-sdk-nodejs');
    const SmsClient = tencentcloud.sms.v20210111.Client;

    const clientConfig = {
      credential: {
        secretId: SMS_CONFIG.secretId,
        secretKey: SMS_CONFIG.secretKey,
      },
      region: SMS_CONFIG.region,
      profile: {
        httpProfile: {
          endpoint: "sms.tencentcloudapi.com",
        },
      },
    };

    const client = new SmsClient(clientConfig);
    const params = {
      PhoneNumberSet: [`+86${phoneNumber}`],
      SmsSdkAppId: SMS_CONFIG.appId,
      SignName: SMS_CONFIG.signName,
      TemplateId: SMS_CONFIG.templateId,
      TemplateParamSet: [code, '5'], // 验证码和有效期（分钟）
    };

    const response = await client.SendSms(params);
    console.log('短信发送响应:', response);

    return { success: true, message: '短信发送成功' };
  } catch (error) {
    console.error('短信发送失败:', error);
    return { success: false, message: '短信发送失败: ' + error.message };
  }
}

// 生成6位随机验证码
function generateVerificationCode() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// 验证用户Token并返回用户数据
async function validateUserToken(token) {
  try {
    const jwt = require('jsonwebtoken');
    const SECRET_KEY = 'novel-app-secret-key-2024';

    // 验证JWT Token
    const decoded = jwt.verify(token, SECRET_KEY);
    const userId = decoded.userId;

    if (!userId) {
      return null;
    }

    // 从数据库获取用户信息
    const userResult = await db.collection('users').where({ id: userId }).get();

    if (userResult.data.length === 0) {
      return null;
    }

    return userResult.data[0];
  } catch (error) {
    console.error('Token验证失败:', error);
    return null;
  }
}

// ⚠️ 已弃用：本地数据库函数（现在使用CloudBase云数据库）
// 加载本地数据库作为备用
// function loadDatabase() {
//   try {
//     const dbPath = path.join(__dirname, 'db.json');
//     const data = fs.readFileSync(dbPath, 'utf8');
//     return JSON.parse(data);
//   } catch (error) {
//     console.log('加载本地数据库失败:', error);
//     return { users: [], packages: [], memberCodes: [], syncData: [] };
//   }
// }

// 保存本地数据库
// function saveDatabase(db) {
//   try {
//     const dbPath = path.join(__dirname, 'db.json');
//     fs.writeFileSync(dbPath, JSON.stringify(db, null, 2));
//     console.log('数据库保存成功');
//   } catch (error) {
//     console.error('保存数据库失败:', error);
//     throw error;
//   }
// }

// 生成会员码
function generateMemberCode(prefix, length) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = prefix;
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 生成会员码工具函数
function generateMemberCode(prefix = 'VIP', length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = prefix;
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 云函数主入口
exports.main = async (event, context) => {
  try {
    const method = event.httpMethod;
    const path = event.path;
    const body = event.body ? JSON.parse(event.body) : {};
    const query = event.queryStringParameters || {};
    const headers = event.headers || {};

    console.log(`请求: ${method} ${path}`);
    console.log('关键请求头:', {
      'x-forwarded-path': headers['x-forwarded-path'],
      'x-original-uri': headers['x-original-uri'],
      'x-real-path': headers['x-real-path'],
      'referer': headers['referer'],
      'host': headers['host']
    });
    console.log('查询参数:', JSON.stringify(query, null, 2));
    console.log('请求体键:', Object.keys(body));

    // 通用CORS头
    const corsHeaders = {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
      'Access-Control-Max-Age': '86400'
    };

    // 创建响应的辅助函数
    const createResponse = (statusCode, data, additionalHeaders = {}) => {
      return {
        statusCode,
        headers: { ...corsHeaders, ...additionalHeaders },
        body: JSON.stringify(data)
      };
    };

    // 处理OPTIONS预检请求
    if (method === 'OPTIONS') {
      return {
        statusCode: 200,
        headers: corsHeaders,
        body: ''
      };
    }

    // CloudBase路径映射解决方案
    // CloudBase将 /api/xxx 映射到 /xxx，去掉了 /api 前缀

    let actualPath = path;

    // 方案1：从查询参数获取路径
    if (query._api_path) {
      actualPath = '/' + query._api_path;
      console.log(`通过查询参数获取路径: ${actualPath}`);
    }
    // 方案2：根据请求特征智能推断路径（考虑CloudBase去掉/api前缀）
    else if (path === '/') {
      // 根据请求方法和请求体内容推断API路径
      if (method === 'POST') {
        if (body.username && body.password && !body.phoneNumber) {
          actualPath = '/auth/login';  // CloudBase去掉了/api前缀
          console.log(`智能推断为登录请求: ${actualPath}`);
        } else if (body.username && body.phoneNumber && body.verificationCode) {
          actualPath = '/auth/register';  // CloudBase去掉了/api前缀
          console.log(`智能推断为注册请求: ${actualPath}`);
        } else if (body.phoneNumber && !body.username) {
          actualPath = '/auth/send-code';  // CloudBase去掉了/api前缀
          console.log(`智能推断为发送验证码请求: ${actualPath}`);
        } else if (body.phoneNumber && body.code) {
          actualPath = '/auth/verify-code';  // CloudBase去掉了/api前缀
          console.log(`智能推断为验证验证码请求: ${actualPath}`);
        } else if (body.code && !body.phoneNumber) {
          actualPath = '/member-code/validate';  // CloudBase去掉了/api前缀
          console.log(`智能推断为验证会员码请求: ${actualPath}`);
        } else if (body.packageId && !body.orderId && !body.memberCode) {
          actualPath = '/orders/create';  // CloudBase去掉了/api前缀
          console.log(`智能推断为创建订单请求: ${actualPath}, packageId: ${body.packageId}`);
        } else if (body.orderId && body.memberCode) {
          actualPath = '/payment/member-code';  // CloudBase去掉了/api前缀
          console.log(`智能推断为会员码支付请求: ${actualPath}`);
        } else {
          actualPath = '/';
          console.log(`无法推断的POST请求，使用根路径。请求体: ${JSON.stringify(body)}`);
        }
      } else if (method === 'GET') {
        // GET请求默认为根路径
        actualPath = '/';
        console.log('GET请求，使用根路径');
      } else {
        actualPath = path;
        console.log(`其他请求方法，直接使用路径: ${actualPath}`);
      }
    }
    // 方案3：CloudBase可能直接传递去掉/api前缀的路径
    else if (path.startsWith('/auth/') || path.startsWith('/health') || path.startsWith('/debug')) {
      actualPath = path;
      console.log(`CloudBase传递的路径（已去掉/api前缀）: ${actualPath}`);
    }
    else {
      actualPath = path;
      console.log(`直接使用路径: ${actualPath}`);
    }

    // 更新path变量用于后续路由
    const routePath = actualPath;

    console.log(`最终路径: ${routePath}`);

    // 创建一个调试响应，显示接收到的所有信息
    if (routePath === '/debug' && method === 'GET') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          debug: true,
          receivedPath: path,
          receivedMethod: method,
          queryParams: query,
          requestBody: body,
          timestamp: new Date().toISOString(),
          fullEvent: event
        }, null, 2)
      };
    }



    // 根路径欢迎消息
    if (routePath === '/' && method === 'GET') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          message: '欢迎使用岱宗文脉API',
          version: '1.0.0',
          debug: {
            receivedPath: path,
            receivedMethod: method,
            timestamp: new Date().toISOString()
          },
          endpoints: {
            health: '/health',
            auth: {
              login: '/auth/login',
              register: '/auth/register',
              sendCode: '/auth/send-code',
              verifyCode: '/auth/verify-code'
            },
            admin: {
              login: '/admin/login'
            },
            packages: '/packages',
            orders: {
              my: '/orders/my',
              create: '/orders/create'
            },
            memberCode: {
              validate: '/member-code/validate'
            },
            sync: {
              upload: '/sync/upload',
              download: '/sync/download',
              stats: '/sync/stats',
              records: '/sync/records',
              trigger: '/sync/trigger',
              queue: '/sync/queue',
              config: '/sync/config',
              clean: '/sync/records/clean',
              export: '/sync/records/export'
            },
            dashboard: '/dashboard',
            users: '/users',
            novels: {
              list: '/novels',
              stats: '/novels/stats'
            },
            members: {
              stats: '/members/stats'
            },
            memberCodes: '/member-codes'
          }
        })
      };
    }

    // 健康检查接口
    if (routePath === '/health' && method === 'GET') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          status: 'healthy',
          message: '岱宗文脉API服务正常运行',
          timestamp: new Date().toISOString(),
          environment: process.env.TCB_ENV || 'development'
        })
      };
    }

    // 处理OPTIONS请求
    if (method === 'OPTIONS') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type,Authorization'
        },
        body: ''
      };
    }

    // 发送验证码接口
    if ((routePath === '/api/auth/send-code' || routePath === '/auth/send-code') && method === 'POST') {
      const { phoneNumber } = body;

      try {
        // 验证手机号格式
        if (!phoneNumber || !phoneNumber.match(/^1[3-9]\d{9}$/)) {
          return createResponse(400, {
            success: false,
            message: '请输入正确的手机号'
          });
        }

        // 检查发送频率限制（1分钟内只能发送一次）
        const lastSendTime = verificationCodes.get(phoneNumber + '_time');
        const now = Date.now();
        if (lastSendTime && (now - lastSendTime) < 60000) {
          const remainingTime = Math.ceil((60000 - (now - lastSendTime)) / 1000);
          return createResponse(429, {
            success: false,
            message: `请等待${remainingTime}秒后再试`
          });
        }

        // 生成验证码
        const code = generateVerificationCode();

        // 发送短信
        const smsResult = await sendSMSCode(phoneNumber, code);

        if (smsResult.success) {
          // 存储验证码（5分钟有效期）
          verificationCodes.set(phoneNumber, {
            code: code,
            expireTime: now + 5 * 60 * 1000, // 5分钟后过期
            attempts: 0 // 验证尝试次数
          });
          verificationCodes.set(phoneNumber + '_time', now);

          console.log(`验证码发送成功: ${phoneNumber} -> ${code}`);

          return createResponse(200, {
            success: true,
            message: SMS_CONFIG.isDevelopment ?
              `验证码发送成功（开发环境：${code}，也可直接使用测试验证码：123456）` :
              '验证码发送成功，请查收短信',
            // 开发环境下返回验证码
            debugCode: SMS_CONFIG.isDevelopment ? code : undefined,
            // 开发环境提示
            devHint: SMS_CONFIG.isDevelopment ? '开发环境可直接使用测试验证码：123456' : undefined
          });
        } else {
          return createResponse(500, {
            success: false,
            message: smsResult.message
          });
        }
      } catch (error) {
        console.error('发送验证码失败:', error);
        return createResponse(500, {
          success: false,
          message: '发送验证码失败'
        });
      }
    }

    // 验证验证码接口
    if ((routePath === '/api/auth/verify-code' || routePath === '/auth/verify-code') && method === 'POST') {
      const { phoneNumber, code } = body;

      try {
        // 验证输入参数
        if (!phoneNumber || !code) {
          return createResponse(400, {
            success: false,
            message: '手机号和验证码不能为空'
          });
        }

        // 开发环境：支持固定测试验证码
        if (SMS_CONFIG.isDevelopment && code === '123456') {
          console.log(`[开发环境] 使用测试验证码验证成功: ${phoneNumber}`);
          return createResponse(200, {
            success: true,
            message: '验证码验证成功（开发环境测试码）'
          });
        }

        // 获取存储的验证码信息
        const storedCodeInfo = verificationCodes.get(phoneNumber);

        if (!storedCodeInfo) {
          const devHint = SMS_CONFIG.isDevelopment ? '（开发环境可直接使用测试验证码：123456）' : '';
          return createResponse(400, {
            success: false,
            message: '请先获取验证码' + devHint
          });
        }

        // 检查验证码是否过期
        if (Date.now() > storedCodeInfo.expireTime) {
          verificationCodes.delete(phoneNumber);
          const devHint = SMS_CONFIG.isDevelopment ? '（开发环境可直接使用测试验证码：123456）' : '';
          return createResponse(400, {
            success: false,
            message: '验证码已过期，请重新获取' + devHint
          });
        }

        // 检查尝试次数（最多3次）
        if (storedCodeInfo.attempts >= 3) {
          verificationCodes.delete(phoneNumber);
          const devHint = SMS_CONFIG.isDevelopment ? '（开发环境可直接使用测试验证码：123456）' : '';
          return createResponse(400, {
            success: false,
            message: '验证码错误次数过多，请重新获取' + devHint
          });
        }

        // 验证验证码
        if (code === storedCodeInfo.code) {
          // 验证成功，删除验证码
          verificationCodes.delete(phoneNumber);
          verificationCodes.delete(phoneNumber + '_time');

          console.log(`验证码验证成功: ${phoneNumber}`);

          return createResponse(200, {
            success: true,
            message: '验证码验证成功'
          });
        } else {
          // 验证失败，增加尝试次数
          storedCodeInfo.attempts++;
          verificationCodes.set(phoneNumber, storedCodeInfo);

          const remainingAttempts = 3 - storedCodeInfo.attempts;
          const devHint = SMS_CONFIG.isDevelopment ? '（开发环境可直接使用测试验证码：123456）' : '';
          return createResponse(400, {
            success: false,
            message: `验证码错误，还可尝试${remainingAttempts}次` + devHint
          });
        }
      } catch (error) {
        console.error('验证验证码失败:', error);
        return createResponse(500, {
          success: false,
          message: '验证验证码失败'
        });
      }
    }

    // 验证会员码接口 - 不需要认证
    if ((routePath === '/api/member-code/validate' || routePath === '/member-code/validate') && method === 'POST') {
      try {
        console.log('🔍 验证会员码请求:', body.code);

        // 查询会员码，兼容 isUsed 为 false 或 undefined 的情况
        const memberCodeResult = await db.collection('memberData').where({
          code: body.code
        }).get();

        console.log('🔍 查询结果:', memberCodeResult.data.length, '个会员码');

        if (memberCodeResult.data.length > 0) {
          const memberCode = memberCodeResult.data[0];
          console.log('🔍 找到会员码:', memberCode);

          // 检查是否已使用（兼容 undefined 和 false）
          if (memberCode.isUsed === true) {
            console.log('❌ 会员码已被使用');
            return {
              statusCode: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: false,
                message: '会员码已被使用'
              })
            };
          }

          // 检查是否过期
          if (!memberCode.expireAt || new Date(memberCode.expireAt) > new Date()) {
            console.log('✅ 会员码有效');
            return {
              statusCode: 200,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: true,
                message: '会员码有效'
              })
            };
          } else {
            console.log('❌ 会员码已过期');
            return {
              statusCode: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: false,
                message: '会员码已过期'
              })
            };
          }
        }

        console.log('❌ 会员码不存在');
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '会员码无效或已过期'
          })
        };
      } catch (error) {
        console.error('验证会员码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '验证会员码失败'
          })
        };
      }
    }

    // 用户退出登录接口
    if ((routePath === '/api/auth/logout' || routePath === '/auth/logout') && method === 'POST') {
      try {
        console.log('🔐 用户退出登录请求');

        // 退出登录通常只需要客户端清除token，服务端可以简单返回成功
        // 如果需要黑名单token，可以在这里实现

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '退出登录成功'
          })
        };
      } catch (error) {
        console.error('退出登录失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '退出登录失败'
          })
        };
      }
    }

    // 用户注册接口
    if ((routePath === '/api/auth/register' || routePath === '/auth/register') && method === 'POST') {
      const { username, phoneNumber, password, nickname, verificationCode, memberCode } = body;

      try {
        console.log('🔐 用户注册请求:', { username, phoneNumber, memberCode, verificationCode });
        console.log('🔐 完整请求体:', JSON.stringify(body, null, 2));

        // 测试数据库连接
        console.log('🔍 测试数据库连接...');
        try {
          const testResult = await db.collection('users').limit(1).get();
          console.log('✅ 数据库连接正常，users集合存在');
        } catch (dbError) {
          console.error('❌ 数据库连接失败:', dbError);
          throw new Error(`数据库连接失败: ${dbError.message}`);
        }

        // 检查用户是否已存在（检查用户名和手机号）
        console.log('🔍 检查用户是否已存在...');
        const existingUserByPhone = await db.collection('users').where({ phoneNumber }).get();
        const existingUserByUsername = await db.collection('users').where({ username }).get();
        console.log('🔍 手机号查询结果:', existingUserByPhone.data.length);
        console.log('🔍 用户名查询结果:', existingUserByUsername.data.length);

        if (existingUserByPhone.data.length > 0) {
          return {
            statusCode: 400,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '手机号已注册'
            })
          };
        }

        if (existingUserByUsername.data.length > 0) {
          return {
            statusCode: 400,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '用户名已存在'
            })
          };
        }

        // 生成用户ID
        const userId = `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        console.log('🆔 生成用户ID:', userId);

        // 创建用户基本信息（优化字段顺序：id和username在最前面）
        const newUser = {
          // 核心标识字段放在最前面
          id: userId,
          username: username || `user${phoneNumber.substring(phoneNumber.length - 4)}`,

          // 联系信息
          phoneNumber,
          email: null,

          // 用户资料
          avatar: null,
          passwordHash: password, // 生产环境应该加密

          // 会员信息
          isMember: false,
          isPermanentMember: false,
          membershipType: 'none',
          memberExpireTime: null,
          memberCode: null,

          // 系统设置
          isDataSyncEnabled: true,
          settings: {
            autoSync: true,
            enableBiometric: false,
            enableNotification: true,
            language: 'zh-CN',
            theme: 'system'
          },

          // 时间戳
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // 如果提供了会员码，验证并激活会员
        if (memberCode) {
          console.log('🔍 注册时验证会员码:', memberCode);

          const memberCodeResult = await db.collection('memberData').where({
            code: memberCode,
            isUsed: false
          }).get();

          console.log('🔍 会员码查询结果:', memberCodeResult.data.length, '个');

          if (memberCodeResult.data.length > 0) {
            const memberCodeData = memberCodeResult.data[0];
            console.log('🔍 找到会员码数据:', memberCodeData);

            // 检查会员码是否过期
            if (!memberCodeData.expireAt || new Date(memberCodeData.expireAt) > new Date()) {
              console.log('✅ 会员码有效，开始激活会员');

              // 根据套餐ID获取套餐信息
              try {
                const packageResult = await db.collection('packages').where({
                  id: memberCodeData.packageId
                }).get();

                if (packageResult.data && packageResult.data.length > 0) {
                  const packageData = packageResult.data[0];
                  console.log('🔍 找到套餐信息:', packageData);

                  // 根据套餐信息激活会员
                  newUser.isMember = true;
                  newUser.memberPackageId = packageData.id;
                  newUser.memberPackageName = packageData.name;

                  if (packageData.durationDays === -1) {
                    // 永久会员
                    newUser.membershipType = 'permanent';
                    newUser.memberExpireTime = null;
                    newUser.isPermanentMember = true;
                  } else {
                    // 有期限会员
                    newUser.membershipType = packageData.durationDays >= 365 ? 'yearly' : 'monthly';
                    newUser.memberExpireTime = new Date(Date.now() + packageData.durationDays * 24 * 60 * 60 * 1000).toISOString();
                    newUser.isPermanentMember = false;
                  }

                  // 保存套餐权限信息
                  newUser.memberPermissions = packageData.permissions || {};
                  newUser.memberFeatures = packageData.features || [];
                } else {
                  console.log('⚠️ 未找到套餐信息，使用兼容逻辑');
                  // 兼容旧的套餐ID
                  if (memberCodeData.packageId === 'pkg_permanent') {
                    newUser.membershipType = 'permanent';
                    newUser.memberExpireTime = null;
                    newUser.isPermanentMember = true;
                    newUser.isMember = true;
                  } else if (memberCodeData.packageId === 'pkg_monthly') {
                    newUser.membershipType = 'monthly';
                    newUser.memberExpireTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();
                    newUser.isPermanentMember = false;
                    newUser.isMember = true;
                  } else {
                    // 默认为永久会员
                    newUser.membershipType = 'permanent';
                    newUser.memberExpireTime = null;
                    newUser.isPermanentMember = true;
                    newUser.isMember = true;
                  }
                }
              } catch (packageError) {
                console.error('获取套餐信息失败:', packageError);
                // 使用默认激活逻辑
                newUser.isMember = true;
                newUser.membershipType = 'permanent';
                newUser.memberExpireTime = null;
                newUser.isPermanentMember = true;
              }

              newUser.memberCode = memberCode;

              // 标记会员码为已使用
              await db.collection('memberData').doc(memberCodeData._id).update({
                isUsed: true,
                usedBy: userId,
                usedAt: new Date().toISOString()
              });

              console.log('✅ 会员码激活成功');
            } else {
              console.log('❌ 会员码已过期');
            }
          } else {
            console.log('❌ 会员码不存在或已使用');
          }
        }

        // 保存用户到数据库
        console.log('💾 开始保存用户到数据库:', newUser.username);
        const userResult = await db.collection('users').add(newUser);
        console.log('✅ 用户保存成功:', userResult.id);

        // 生成JWT Token
        console.log('🔑 开始生成JWT Token');
        const jwt = require('jsonwebtoken');
        const SECRET_KEY = 'novel-app-secret-key-2024';
        const token = jwt.sign({ userId }, SECRET_KEY, { expiresIn: '24h' });
        const refreshToken = jwt.sign({ userId }, SECRET_KEY, { expiresIn: '30d' });
        console.log('✅ JWT Token生成成功');

        console.log('🎉 用户注册完成:', userId);
        return createResponse(200, {
          success: true,
          message: '注册成功',
          data: {
            token,
            refreshToken: refreshToken,
            user: newUser,
            expiresIn: 86400
          }
        });
      } catch (error) {
        console.error('❌ 用户注册失败 - 详细错误:', error);
        console.error('❌ 错误堆栈:', error.stack);
        console.error('❌ 错误消息:', error.message);

        // 返回更详细的错误信息用于调试
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '注册失败',
            error: error.message || '未知错误',
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined
          })
        };
      }
    }

    // 用户登录接口
    if ((routePath === '/api/auth/login' || routePath === '/auth/login') && method === 'POST') {
      const { username, password } = body;

      try {
        // 查找用户（支持用户名登录）
        const userResult = await db.collection('users').where({ username }).get();

        if (userResult.data.length === 0) {
          return createResponse(400, {
            success: false,
            message: '用户名或密码错误'
          });
        }

        const user = userResult.data[0];

        // 验证密码（生产环境应该使用加密比较）
        if (user.passwordHash !== password) {
          return createResponse(400, {
            success: false,
            message: '用户名或密码错误'
          });
        }

        // 生成JWT Token
        const jwt = require('jsonwebtoken');
        const SECRET_KEY = 'novel-app-secret-key-2024';
        const token = jwt.sign({ userId: user.id }, SECRET_KEY, { expiresIn: '24h' });
        const refreshToken = jwt.sign({ userId: user.id }, SECRET_KEY, { expiresIn: '30d' });

        return createResponse(200, {
          success: true,
          message: '登录成功',
          data: {
            token,
            refreshToken: refreshToken,
            user: user,
            expiresIn: 86400
          }
        });
      } catch (error) {
        console.error('用户登录失败:', error);
        return createResponse(500, {
          success: false,
          message: '登录失败'
        });
      }
    }

    // 创建单个会员码接口
    if ((routePath === '/api/admin/member-code/create' || routePath === '/admin/member-code/create') && method === 'POST') {
      const { packageId, expireAt, batchId, customCode, prefix } = body;

      try {
        // 生成或使用自定义会员码
        let code = customCode;
        if (!code) {
          let attempts = 0;
          do {
            code = generateMemberCode(prefix || 'VIP');
            attempts++;
            if (attempts > 100) {
              return {
                statusCode: 500,
                headers: {
                  'Content-Type': 'application/json',
                  'Access-Control-Allow-Origin': '*'
                },
                body: JSON.stringify({
                  success: false,
                  message: '生成唯一会员码失败，请重试'
                })
              };
            }

            // 检查会员码是否已存在
            const existingCode = await db.collection('memberCodes').where({ code }).get();
            if (existingCode.data.length === 0) break;
          } while (true);
        } else {
          // 检查自定义会员码是否已存在
          const existingCode = await db.collection('memberCodes').where({ code }).get();
          if (existingCode.data.length > 0) {
            return {
              statusCode: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: false,
                message: '会员码已存在'
              })
            };
          }
        }

        const newMemberCode = {
          code,
          packageId: packageId || 'pkg_permanent',
          isUsed: false,
          usedBy: null,
          usedAt: null,
          expireAt: expireAt || null,
          batchId: batchId || `batch_${Date.now()}`,
          createdAt: new Date().toISOString()
        };

        const result = await db.collection('memberCodes').add(newMemberCode);

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: { ...newMemberCode, _id: result.id },
            message: '会员码创建成功'
          })
        };
      } catch (error) {
        console.error('创建会员码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '创建会员码失败'
          })
        };
      }
    }

    // 批量创建会员码接口
    if ((path === '/api/admin/member-code/batch-create' || path === '/admin/member-code/batch-create') && method === 'POST') {
      const { packageId, count, expireAt, batchId, prefix } = body;

      try {
        const createdCodes = [];
        const currentBatchId = batchId || `batch_${Date.now()}`;
        const codeCount = Math.min(count || 10, 100); // 限制最多100个

        for (let i = 0; i < codeCount; i++) {
          let code;
          let attempts = 0;

          // 生成唯一的会员码
          do {
            code = generateMemberCode(prefix || 'VIP');
            attempts++;
            if (attempts > 100) {
              break;
            }

            const existingCode = await db.collection('memberData').where({ code }).get();
            if (existingCode.data.length === 0) break;
          } while (true);

          if (attempts <= 100) {
            const newMemberCode = {
              code,
              packageId: packageId || 'pkg_permanent',
              isUsed: false,
              usedBy: null,
              usedAt: null,
              expireAt: expireAt || null,
              batchId: currentBatchId,
              createdAt: new Date().toISOString()
            };

            const result = await db.collection('memberData').add(newMemberCode);
            createdCodes.push({ ...newMemberCode, _id: result.id });
          }
        }

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              batchId: currentBatchId,
              count: createdCodes.length,
              codes: createdCodes
            },
            message: `成功创建 ${createdCodes.length} 个会员码`
          })
        };
      } catch (error) {
        console.error('批量创建会员码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '批量创建会员码失败'
          })
        };
      }
    }

    // 获取会员码列表接口
    if ((path === '/api/admin/member-code/list' || path === '/admin/member-code/list') && method === 'GET') {
      const { page = 1, limit = 20, isUsed, packageId, batchId } = query;

      try {
        let queryConditions = {};

        // 构建查询条件
        if (isUsed !== undefined) {
          queryConditions.isUsed = isUsed === 'true';
        }

        if (packageId) {
          queryConditions.packageId = packageId;
        }

        if (batchId) {
          queryConditions.batchId = batchId;
        }

        // 查询会员码
        const memberCodesResult = await db.collection('memberData')
          .where(queryConditions)
          .orderBy('createdAt', 'desc')
          .skip((parseInt(page) - 1) * parseInt(limit))
          .limit(parseInt(limit))
          .get();

        // 获取总数
        const totalResult = await db.collection('memberData').where(queryConditions).count();

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              codes: memberCodesResult.data,
              pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: totalResult.total,
                totalPages: Math.ceil(totalResult.total / parseInt(limit))
              }
            }
          })
        };
      } catch (error) {
        console.error('获取会员码列表失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取会员码列表失败'
          })
        };
      }
    }

    // 获取会员码统计信息接口
    if ((path === '/api/admin/member-code/stats' || path === '/admin/member-code/stats') && method === 'GET') {
      try {
        const allCodesResult = await db.collection('memberData').get();
        const memberCodes = allCodesResult.data;

        const stats = {
          total: memberCodes.length,
          used: memberCodes.filter(code => code.isUsed).length,
          unused: memberCodes.filter(code => !code.isUsed).length,
          expired: memberCodes.filter(code =>
            code.expireAt && new Date(code.expireAt) < new Date()
          ).length,
          byPackage: {},
          byBatch: {}
        };

        // 按套餐统计
        memberCodes.forEach(code => {
          if (!stats.byPackage[code.packageId]) {
            stats.byPackage[code.packageId] = { total: 0, used: 0, unused: 0 };
          }
          stats.byPackage[code.packageId].total++;
          if (code.isUsed) {
            stats.byPackage[code.packageId].used++;
          } else {
            stats.byPackage[code.packageId].unused++;
          }
        });

        // 按批次统计
        memberCodes.forEach(code => {
          if (!stats.byBatch[code.batchId]) {
            stats.byBatch[code.batchId] = { total: 0, used: 0, unused: 0 };
          }
          stats.byBatch[code.batchId].total++;
          if (code.isUsed) {
            stats.byBatch[code.batchId].used++;
          } else {
            stats.byBatch[code.batchId].unused++;
          }
        });

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: stats
          })
        };
      } catch (error) {
        console.error('获取会员码统计失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取会员码统计失败'
          })
        };
      }
    }

    // 删除会员码接口
    if ((path.startsWith('/api/admin/member-code/') || path.startsWith('/admin/member-code/')) && method === 'DELETE') {
      const codeToDelete = path.split('/').pop();

      try {
        const memberCodeResult = await db.collection('memberData').where({ code: codeToDelete }).get();

        if (memberCodeResult.data.length === 0) {
          return {
            statusCode: 404,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '会员码不存在'
            })
          };
        }

        const memberCode = memberCodeResult.data[0];
        if (memberCode.isUsed) {
          return {
            statusCode: 400,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '已使用的会员码不能删除'
            })
          };
        }

        await db.collection('memberData').doc(memberCode._id).remove();

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '会员码删除成功'
          })
        };
      } catch (error) {
        console.error('删除会员码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '删除会员码失败'
          })
        };
      }
    }

    // 获取云存储上传凭证接口
    if (path === '/sync/upload-token' && method === 'POST') {
      try {
        console.log('获取云存储上传凭证请求');

        // 验证用户身份
        const authHeader = event.headers?.authorization || event.headers?.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '缺少认证信息'
            })
          };
        }

        const token = authHeader.substring(7);
        const userData = await validateUserToken(token);
        if (!userData) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '用户认证失败'
            })
          };
        }

        // 检查会员状态
        if (!userData.isValidMember) {
          return {
            statusCode: 403,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '非会员用户无法使用数据同步功能'
            })
          };
        }

        // 生成云存储文件路径
        const timestamp = new Date().toISOString();
        const fileName = `sync-data/${userData.userId}/${timestamp}.json`;

        // 返回上传信息（简化版本，实际项目中需要生成真正的预签名URL）
        const uploadInfo = {
          fileName: fileName,
          uploadPath: `/sync/upload-direct`, // 直接上传到云存储的路径
          timestamp: timestamp,
          expires: Date.now() + 3600000, // 1小时后过期
          userId: userData.userId
        };

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '上传凭证获取成功',
            data: uploadInfo
          })
        };

      } catch (error) {
        console.error('获取上传凭证失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取上传凭证失败: ' + error.message
          })
        };
      }
    }

    // 云存储直接上传接口
    if (path === '/sync/upload-direct' && method === 'POST') {
      try {
        console.log('云存储直接上传请求');

        // 验证用户身份
        const authHeader = event.headers?.authorization || event.headers?.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '缺少认证信息'
            })
          };
        }

        const token = authHeader.substring(7);
        const userData = await validateUserToken(token);
        if (!userData) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '用户认证失败'
            })
          };
        }

        // 解析请求数据
        const body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;

        // 验证数据
        if (!body || !body.data) {
          throw new Error('缺少同步数据');
        }

        console.log('接收到同步数据，大小:', JSON.stringify(body).length, '字节');

        // 直接保存到数据库（绕过6MB限制）
        const updateData = {
          syncData: body.data,
          syncTimestamp: body.timestamp || new Date().toISOString(),
          syncUpdatedAt: new Date().toISOString()
        };

        console.log('准备更新用户数据，用户ID:', userData.userId);
        await db.collection('users').doc(userData._id).update(updateData);
        console.log('用户数据更新成功');

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '数据同步上传成功',
            timestamp: updateData.syncTimestamp
          })
        };

      } catch (error) {
        console.error('云存储直接上传失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '数据上传失败: ' + error.message
          })
        };
      }
    }

    // COS直传签名接口已删除 - 统一使用CloudBase数据库API

    // CloudBase存储直传接口已删除 - 统一使用 /sync/upload 接口

    // 处理上传文件通知接口已删除 - 不再需要COS相关的文件处理

    // 数据同步上传（分批）- 保留作为备用
    if (path === '/sync/upload' && method === 'POST') {
      try {
        // 验证用户身份
        const authHeader = event.headers?.authorization || event.headers?.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '未提供认证Token'
            })
          };
        }

        const token = authHeader.substring(7);

        // 使用JWT验证Token
        const jwt = require('jsonwebtoken');
        const SECRET_KEY = 'novel-app-secret-key-2024';

        let userId;
        try {
          const decoded = jwt.verify(token, SECRET_KEY);
          userId = decoded.userId;

          if (!userId) {
            throw new Error('No userId in token');
          }
        } catch (e) {
          console.error('Token验证失败:', e);
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '无效的认证Token: ' + e.message
            })
          };
        }

        // 验证用户是否存在且为会员
        const user = await db.collection('users').where({
          id: userId
        }).get();

        if (user.data.length === 0) {
          return {
            statusCode: 404,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '用户不存在'
            })
          };
        }

        const userData = user.data[0];
        if (!userData.isMember) {
          return {
            statusCode: 403,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '数据同步功能仅限会员使用'
            })
          };
        }

        // 解析请求数据
        const requestBody = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;

        // 验证请求数据
        if (!requestBody || typeof requestBody !== 'object') {
          throw new Error('请求体为空或格式不正确');
        }

        // 支持新的分块上传格式
        const chunkInfo = requestBody.chunkInfo;
        const data = requestBody.data;
        const timestamp = requestBody.timestamp;

        // 计算数据大小
        const dataSizeKB = (JSON.stringify(data).length / 1024).toFixed(2);

        console.log('接收到同步上传请求:');
        console.log('- 用户ID:', userId);
        console.log('- 数据大小:', dataSizeKB, 'KB');
        console.log('- 时间戳:', timestamp);
        console.log('- 分块信息:', chunkInfo);

        if (chunkInfo) {
          console.log(`用户 ${userId} 上传数据块 ${chunkInfo.index}/${chunkInfo.total}:`, chunkInfo.type);
          console.log(`块大小: ${dataSizeKB} KB`);
        } else {
          console.log(`用户 ${userId} 上传完整数据`);
          console.log(`数据大小: ${dataSizeKB} KB`);
        }

        // 获取现有同步数据
        let existingSyncData = userData.syncData || {};

        // 处理分块上传
        if (chunkInfo && chunkInfo.isChunked) {
          console.log('处理分块数据:', chunkInfo);

          // 根据数据类型合并
          if (chunkInfo.type === 'userSettings') {
            Object.assign(existingSyncData, data);
          } else if (chunkInfo.type === 'knowledgeDocuments') {
            existingSyncData.knowledgeDocuments = data.knowledgeDocuments;
          } else if (chunkInfo.type.startsWith('novels_chunk_') || chunkInfo.type.startsWith('novels_batch_')) {
            // 合并小说分块数据 - 支持单章节上传
            console.log('处理小说分块数据:', chunkInfo.type);

            if (!existingSyncData.novels) {
              existingSyncData.novels = [];
            }

            // 从data中提取小说数据
            if (data.novels && Array.isArray(data.novels)) {
              console.log('处理小说分块，收到:', data.novels.length, '本小说');

              // 处理每个小说块
              for (const novelChunk of data.novels) {
                if (novelChunk.chunkInfo) {
                  const { novelIndex, chapterIndex, totalChapters, chapterTitle, isLastChapter, originalChapterIndex } = novelChunk.chunkInfo;
                  const actualChapterIndex = originalChapterIndex !== undefined ? originalChapterIndex : chapterIndex;
                  console.log(`处理小说块 - 小说索引: ${novelIndex}, 章节: ${actualChapterIndex + 1}/${totalChapters}, 标题: ${chapterTitle}`);

                  // 查找或创建对应的小说记录
                  let existingNovel = existingSyncData.novels.find(n =>
                    n.title === novelChunk.title ||
                    (n.originalIndex !== undefined && n.originalIndex === novelIndex)
                  );

                  if (!existingNovel) {
                    // 创建新的小说记录
                    existingNovel = {
                      ...novelChunk,
                      chapters: [],
                      originalIndex: novelIndex
                    };
                    delete existingNovel.chunkInfo; // 移除分块信息
                    existingSyncData.novels.push(existingNovel);
                    console.log(`创建新小说记录: ${novelChunk.title}`);
                  }

                  // 合并章节数据 - 支持内容分块
                  if (novelChunk.chapters && Array.isArray(novelChunk.chapters)) {
                    for (const chapter of novelChunk.chapters) {
                      // 检查是否是内容分块
                      if (chapter.contentChunkInfo) {
                        const { chunkIndex, totalChunks, isLastChunk } = chapter.contentChunkInfo;
                        console.log(`处理内容分块: ${chapterTitle} - 块 ${chunkIndex + 1}/${totalChunks}`);

                        // 查找或创建对应的章节记录 - 修复：使用章节标题和原始章节索引匹配
                        let existingChapter = existingNovel.chapters.find(c =>
                          c.title === chapter.title ||
                          (c.originalChapterIndex !== undefined && c.originalChapterIndex === actualChapterIndex)
                        );

                        if (!existingChapter) {
                          // 创建新的章节记录
                          existingChapter = {
                            ...chapter,
                            number: actualChapterIndex + 1, // 前端期望的字段名，从1开始
                            chapterIndex: actualChapterIndex, // 保留索引字段
                            originalChapterIndex: actualChapterIndex, // 添加原始索引标记
                            content: '',
                            contentChunks: []
                          };
                          delete existingChapter.contentChunkInfo;
                          existingNovel.chapters.push(existingChapter);
                          console.log(`创建新章节记录: ${chapterTitle} (number: ${actualChapterIndex + 1}, 索引: ${actualChapterIndex})`);
                        }

                        // 存储内容块
                        if (!existingChapter.contentChunks) {
                          existingChapter.contentChunks = [];
                        }

                        existingChapter.contentChunks[chunkIndex] = chapter.content || '';

                        // 如果是最后一块，合并所有内容
                        if (isLastChunk) {
                          existingChapter.content = existingChapter.contentChunks.join('');
                          delete existingChapter.contentChunks; // 清理临时数据
                          console.log(`章节内容合并完成: ${chapterTitle}`);
                        }
                      } else {
                        // 普通章节处理
                        const existingChapterIndex = existingNovel.chapters.findIndex(c =>
                          c.title === chapter.title || c.chapterIndex === actualChapterIndex
                        );

                        if (existingChapterIndex === -1) {
                          // 添加章节索引信息
                          chapter.number = actualChapterIndex + 1; // 前端期望的字段名，从1开始
                          chapter.chapterIndex = actualChapterIndex;
                          chapter.originalChapterIndex = actualChapterIndex;
                          existingNovel.chapters.push(chapter);
                          console.log(`添加章节: ${chapterTitle} (number: ${actualChapterIndex + 1}, 索引: ${actualChapterIndex})`);
                        } else {
                          // 更新现有章节
                          existingNovel.chapters[existingChapterIndex] = {
                            ...chapter,
                            number: actualChapterIndex + 1, // 前端期望的字段名，从1开始
                            chapterIndex: actualChapterIndex,
                            originalChapterIndex: actualChapterIndex
                          };
                          console.log(`更新章节: ${chapterTitle} (number: ${actualChapterIndex + 1}, 索引: ${actualChapterIndex})`);
                        }
                      }
                    }

                    // 按章节索引排序
                    existingNovel.chapters.sort((a, b) => (a.chapterIndex || 0) - (b.chapterIndex || 0));
                  }
                } else {
                  // 没有分块信息的传统处理方式
                  console.log('传统小说数据合并:', novelChunk.title);
                  existingSyncData.novels.push(novelChunk);
                }
              }
            } else {
              console.log('小说数据格式错误:', typeof data.novels);
            }
          } else if (chunkInfo.type === 'characterCards') {
            existingSyncData.characterCards = data.characterCards;
          } else if (chunkInfo.type === 'characterTypes') {
            existingSyncData.characterTypes = data.characterTypes;
          } else {
            console.log('未知的分块类型:', chunkInfo.type);
          }
        } else {
          // 传统的完整数据上传 - 智能合并而不是覆盖
          console.log('处理完整数据上传 - 智能合并模式');

          if (data && typeof data === 'object') {
            // 对于每种数据类型，进行智能合并
            Object.keys(data).forEach(key => {
              if (key === 'novels' && Array.isArray(data.novels)) {
                // 小说数据需要特殊处理 - 合并而不是覆盖
                if (!existingSyncData.novels) {
                  existingSyncData.novels = [];
                }

                console.log(`合并小说数据: 现有 ${existingSyncData.novels.length} 本，新增 ${data.novels.length} 本`);

                // 对于每个新小说，检查是否已存在
                for (const newNovel of data.novels) {
                  const existingIndex = existingSyncData.novels.findIndex(existing =>
                    existing.title === newNovel.title ||
                    (existing.id && newNovel.id && existing.id === newNovel.id)
                  );

                  if (existingIndex >= 0) {
                    // 更新现有小说
                    console.log(`更新现有小说: ${newNovel.title}`);
                    existingSyncData.novels[existingIndex] = newNovel;
                  } else {
                    // 添加新小说
                    console.log(`添加新小说: ${newNovel.title}`);
                    existingSyncData.novels.push(newNovel);
                  }
                }
              } else {
                // 其他数据类型直接覆盖
                existingSyncData[key] = data[key];
              }
            });
          }
        }

        // 使用优化的分块存储策略
        const syncTimestamp = timestamp || new Date().toISOString();

        try {
          console.log('准备存储数据，用户ID:', userId);
          const dataSize = JSON.stringify(existingSyncData).length;
          console.log('数据大小:', dataSize, '字节');

          // 对于小数据（小于100KB），直接存储在用户记录中
          // 对于大数据，使用分块存储
          if (dataSize < 100 * 1024) {
            console.log('数据较小，直接存储在用户记录中');
            // 直接更新用户记录中的syncData字段
            await db.collection('users').doc(userData._id).update({
              syncData: existingSyncData,
              syncTimestamp: syncTimestamp,
              syncUpdatedAt: new Date().toISOString(),
              lastSyncSize: dataSize,
              syncMethod: 'direct_storage'
            });
            console.log('✅ 数据直接存储成功');
          } else {
            console.log('数据较大，使用分块存储');
            // 使用分块存储处理大数据
            const success = await storeUserDataInChunks(userId, existingSyncData, syncTimestamp, chunkInfo);

            if (!success) {
              throw new Error('分块存储失败');
            }

            // 对于分块存储的大数据，更新用户的同步元数据（不包含实际数据）
            const updateData = {
              syncTimestamp: syncTimestamp,
              syncUpdatedAt: new Date().toISOString(),
              lastSyncSize: dataSize,
              syncMethod: 'chunked_storage'
            };

            // 如果是分块上传，记录分块信息
            if (chunkInfo) {
              updateData.lastChunkInfo = chunkInfo;
            }

            await db.collection('users').doc(userData._id).update(updateData);
            console.log('用户元数据更新成功');
          }
        } catch (dbError) {
          console.error('数据库更新失败:', dbError);
          throw new Error(`数据库更新失败: ${dbError.message}`);
        }

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: chunkInfo ? `数据块 ${chunkInfo.index}/${chunkInfo.total} 同步成功` : '数据同步成功',
            dataSize: dataSizeKB + ' KB',
            timestamp: syncTimestamp,
            chunkInfo: chunkInfo
          })
        };
      } catch (error) {
        console.error('数据同步上传失败:', error);
        console.error('错误详情:', error.message);
        console.error('错误堆栈:', error.stack);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '数据同步上传失败',
            error: error.message
          })
        };
      }
    }

    // 获取同步记录接口（管理员和用户通用）
    if (routePath === '/sync/records' && method === 'GET') {
      try {
        const authHeader = headers.authorization || headers.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return createResponse(401, {
            success: false,
            message: '未提供认证令牌'
          });
        }

        const token = authHeader.replace('Bearer ', '');

        // 先尝试验证管理员token
        const jwt = require('jsonwebtoken');
        const SECRET_KEY = 'novel-app-secret-key-2024';

        let isAdmin = false;
        let userInfo = null;

        try {
          const decoded = jwt.verify(token, SECRET_KEY);
          if (decoded.isAdmin) {
            isAdmin = true;
            console.log('管理员访问同步记录');
          } else {
            // 普通用户token
            userInfo = await validateUserToken(token);
            if (!userInfo) {
              return createResponse(401, {
                success: false,
                message: '无效的认证Token'
              });
            }
          }
        } catch (jwtError) {
          return createResponse(401, {
            success: false,
            message: '无效的认证Token'
          });
        }

        if (isAdmin) {
          // 管理员：获取所有用户的同步记录
          const usersResult = await db.collection('users').get();
          const users = usersResult.data || [];

          // 格式化同步记录
          const records = users
            .filter(user => user.syncData && user.syncTimestamp)
            .map(user => {
              const syncData = user.syncData || {};
              const dataSize = JSON.stringify(syncData).length;

              // 分析同步内容
              const syncContent = [];
              if (syncData.novels && syncData.novels.length > 0) syncContent.push('小说');
              if (syncData.characterCards && syncData.characterCards.length > 0) syncContent.push('角色卡片');
              if (syncData.knowledgeDocuments && syncData.knowledgeDocuments.length > 0) syncContent.push('知识库');
              if (syncData.stylePackages && syncData.stylePackages.length > 0) syncContent.push('写作风格');
              if (syncData.userSettings) syncContent.push('用户设置');

              // 判断同步方式
              let syncMethod = 'api';
              if (user.syncMethod) {
                syncMethod = user.syncMethod;
              } else if (dataSize > 1024 * 1024) { // 大于1MB
                syncMethod = 'chunked';
              } else if (user.directUpload) {
                syncMethod = 'direct';
              }

              return {
                id: `sync_${user.userId}_${new Date(user.syncTimestamp).getTime()}`,
                userId: user.userId,
                username: user.username || user.phoneNumber || '未知用户',
                syncTime: user.syncTimestamp,
                status: 'success',
                syncContent: syncContent.join(', ') || '无数据',
                syncMethod: syncMethod,
                dataSize: dataSize,
                error: null,
                duration: Math.floor(Math.random() * 5000) + 1000 // 模拟耗时1-6秒
              };
            })
            .sort((a, b) => new Date(b.syncTime).getTime() - new Date(a.syncTime).getTime());

          // 分页处理
          const page = parseInt(query.page) || 1;
          const size = parseInt(query.size) || 20;
          const startIndex = (page - 1) * size;
          const endIndex = startIndex + size;
          const paginatedRecords = records.slice(startIndex, endIndex);

          return createResponse(200, {
            success: true,
            data: {
              syncRecords: paginatedRecords,
              total: records.length
            },
            message: '获取同步记录成功'
          });
        } else {
          // 普通用户：获取自己的同步记录
          const syncRecordsResult = await db.collection('sync_records')
            .where({ userId: userInfo.id })
            .orderBy('syncTime', 'desc')
            .limit(50)
            .get();

          const records = syncRecordsResult.data || [];

          return createResponse(200, {
            success: true,
            data: {
              records: records,
              total: records.length
            },
            message: '获取同步记录成功'
          });
        }

      } catch (error) {
        console.error('获取同步记录失败:', error);
        return createResponse(500, {
          success: false,
          message: '获取同步记录失败: ' + error.message
        });
      }
    }

    // 数据同步下载
    if (path === '/sync/download' && method === 'GET') {
      try {
        // 验证用户身份
        const authHeader = event.headers?.authorization || event.headers?.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '未提供认证Token'
            })
          };
        }

        const token = authHeader.substring(7);

        // 使用JWT验证Token
        const jwt = require('jsonwebtoken');
        const SECRET_KEY = 'novel-app-secret-key-2024';

        let userId;
        try {
          const decoded = jwt.verify(token, SECRET_KEY);
          userId = decoded.userId;

          if (!userId) {
            throw new Error('No userId in token');
          }
        } catch (e) {
          console.error('Token验证失败:', e);
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '无效的认证Token'
            })
          };
        }

        // 验证用户是否存在且为会员
        const user = await db.collection('users').where({
          id: userId
        }).get();

        if (user.data.length === 0) {
          return {
            statusCode: 404,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '用户不存在'
            })
          };
        }

        const userData = user.data[0];
        if (!userData.isMember) {
          return {
            statusCode: 403,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '数据同步功能仅限会员使用'
            })
          };
        }

        // 从用户记录中获取同步数据
        const data = userData.syncData || {};
        const timestamp = userData.syncTimestamp || null;

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '数据同步下载成功',
            data: data,
            timestamp: timestamp
          })
        };
      } catch (error) {
        console.error('数据同步下载失败:', error);
        console.error('错误详情:', error.message);
        console.error('错误堆栈:', error.stack);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '数据同步下载失败',
            error: error.message
          })
        };
      }
    }

    // CloudBase数据库下载接口（重命名，去掉"直传"概念）
    if ((path === '/sync/download-direct' || path === '/api/sync/download-direct' || path === '/sync/download' || path === '/api/sync/download') && method === 'GET') {
      try {
        console.log('收到CloudBase数据库下载请求');

        // 验证用户身份
        const authHeader = event.headers.authorization || event.headers.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '缺少认证信息'
            })
          };
        }

        const token = authHeader.substring(7);

        // 使用JWT验证Token
        const jwt = require('jsonwebtoken');
        const SECRET_KEY = 'novel-app-secret-key-2024';

        let userId;
        try {
          const decoded = jwt.verify(token, SECRET_KEY);
          userId = decoded.userId;

          if (!userId) {
            throw new Error('No userId in token');
          }
        } catch (e) {
          console.error('Token验证失败:', e);
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '无效的认证Token'
            })
          };
        }

        // 验证用户是否存在且为会员
        const user = await db.collection('users').where({
          id: userId
        }).get();

        if (!user.data || user.data.length === 0) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '用户不存在'
            })
          };
        }

        const userData = user.data[0];
        if (!userData.isMember) {
          return {
            statusCode: 403,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '数据同步功能仅限会员使用'
            })
          };
        }

        // 优先从分块存储中恢复数据
        let data = await retrieveUserDataFromChunks(userId);
        let timestamp = userData.syncTimestamp || null;

        // 如果分块存储中没有数据，则从用户记录中获取（兼容旧数据）
        if (!data) {
          console.log('分块存储中没有数据，从用户记录中获取');
          data = userData.syncData || {};
        }

        console.log('CloudBase数据库下载成功，数据大小:', JSON.stringify(data).length, '字节');

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: 'CloudBase数据库下载成功',
            data: data,
            timestamp: timestamp
          })
        };
      } catch (error) {
        console.error('CloudBase数据库下载失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: 'CloudBase数据库下载失败',
            error: error.message
          })
        };
      }
    }

    // 获取会员套餐（公开接口）
    if (routePath === '/packages' && method === 'GET') {
      try {
        console.log('获取会员套餐（从CloudBase数据库）');

        let packages = [];

        try {
          // 尝试从CloudBase数据库获取套餐信息
          const packagesResult = await db.collection('packages').get();
          const allPackages = packagesResult.data || [];
          // 只返回启用且可购买的套餐，并转换为Flutter应用期望的格式
          packages = allPackages
            .filter(pkg => pkg.isActive && !pkg.displayOnly)
            .map(pkg => ({
              id: pkg.id,
              name: pkg.name,
              description: pkg.description || '',
              price: pkg.price || 0,
              durationDays: pkg.durationDays || 30,
              features: pkg.features || [],
              limits: {
                maxChaptersPerNovel: pkg.permissions?.maxChaptersPerNovel || -1,
                maxKnowledgeDocuments: pkg.permissions?.maxKnowledgeDocuments || -1,
                canUseExtendedFeatures: pkg.permissions?.canUseExtendedFeatures !== false,
                maxNovelsPerDay: pkg.permissions?.maxNovelsPerDay || -1,
                maxWordsPerGeneration: pkg.permissions?.maxWordsPerGeneration || -1,
                canExportToMultipleFormats: pkg.permissions?.canExportToMultipleFormats !== false,
                canUseAdvancedAI: pkg.permissions?.canUseAdvancedAI !== false,
                maxCustomCharacterTypes: pkg.permissions?.maxCustomCharacterTypes || -1
              },
              isActive: pkg.isActive,
              sortOrder: pkg.sortOrder || 0,
              createdAt: pkg.createdAt,
              updatedAt: pkg.updatedAt
            }));
          console.log('从CloudBase获取到套餐数量:', packages.length);
        } catch (dbError) {
          console.log('packages集合不存在，使用默认套餐数据');
          // 如果packages集合不存在，返回默认套餐数据（Flutter格式）
          packages = [
            {
              id: 'basic',
              name: '基础版',
              description: '适合个人用户的基础功能',
              price: 0,
              durationDays: 30,
              features: ['基础AI创作功能', '本地数据存储', '基础模板库'],
              limits: {
                maxChaptersPerNovel: 50,
                maxKnowledgeDocuments: 5,
                canUseExtendedFeatures: false,
                maxNovelsPerDay: 3,
                maxWordsPerGeneration: 2000,
                canExportToMultipleFormats: true,
                canUseAdvancedAI: false,
                maxCustomCharacterTypes: 3
              },
              isActive: true,
              sortOrder: 0,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            },
            {
              id: 'premium',
              name: '高级版',
              description: '适合专业创作者的高级功能',
              price: 29.9,
              durationDays: 30,
              features: ['高级AI创作功能', '云端数据同步', '完整模板库', '角色管理系统'],
              limits: {
                maxChaptersPerNovel: -1,
                maxKnowledgeDocuments: -1,
                canUseExtendedFeatures: true,
                maxNovelsPerDay: -1,
                maxWordsPerGeneration: -1,
                canExportToMultipleFormats: true,
                canUseAdvancedAI: true,
                maxCustomCharacterTypes: -1
              },
              isActive: true,
              sortOrder: 1,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          ];
        }

        return createResponse(200, {
          success: true,
          data: packages
        });
      } catch (error) {
        console.error('获取套餐失败:', error);
        return createResponse(500, {
          success: false,
          message: '获取套餐失败: ' + error.message
        });
      }
    }

    // 获取所有套餐（管理员接口）
    if (routePath === '/packages' && method === 'GET' && headers.authorization) {
      try {
        console.log('获取所有套餐（管理员，从CloudBase数据库）');

        let packages = [];

        try {
          // 尝试从CloudBase数据库获取所有套餐信息
          const packagesResult = await db.collection('packages').get();
          packages = packagesResult.data || [];
          console.log('从CloudBase获取到套餐数量:', packages.length);
        } catch (dbError) {
          console.log('packages集合不存在，使用默认套餐数据');
          // 如果packages集合不存在，返回默认套餐数据
          packages = [
            {
              id: 'basic',
              name: '基础版',
              description: '适合个人用户的基础功能',
              price: 0,
              duration: 30,
              features: ['基础AI创作功能', '本地数据存储', '基础模板库'],
              isActive: true,
              createdAt: new Date().toISOString()
            },
            {
              id: 'premium',
              name: '高级版',
              description: '适合专业创作者的高级功能',
              price: 29.9,
              duration: 30,
              features: ['高级AI创作功能', '云端数据同步', '完整模板库', '角色管理系统'],
              isActive: true,
              createdAt: new Date().toISOString()
            }
          ];
        }

        return createResponse(200, {
          success: true,
          data: packages
        });
      } catch (error) {
        console.error('获取所有套餐失败:', error);
        return createResponse(500, {
          success: false,
          message: '获取所有套餐失败: ' + error.message
        });
      }
    }

    // 添加套餐（管理员接口）
    if (routePath === '/packages' && method === 'POST') {
      try {
        console.log('添加套餐（到CloudBase数据库）');

        const {
          name,
          price,
          unit,
          features,
          isPopular,
          isActive,
          description,
          durationDays,
          permissions,
          displayOnly
        } = body;

        if (!name || price === undefined) {
          return createResponse(400, {
            success: false,
            message: '套餐名称和价格不能为空'
          });
        }

        // 从CloudBase数据库检查套餐名称是否已存在
        const existingPackageResult = await db.collection('packages').where({
          name: name
        }).get();

        if (existingPackageResult.data && existingPackageResult.data.length > 0) {
          return createResponse(400, {
            success: false,
            message: '套餐名称已存在'
          });
        }

        const packageId = `pkg_${Date.now()}`;
        const newPackage = {
          id: packageId,
          name,
          description: description || '',
          price: parseFloat(price),
          unit: unit || '一次性',
          durationDays: durationDays || 30, // 默认30天
          features: features || [],
          // 存储权限数据（后台管理用）
          permissions: permissions || {
            maxNovelsPerDay: -1,
            maxChaptersPerNovel: -1,
            maxKnowledgeDocuments: -1,
            canUseExtendedFeatures: true,
            maxWordsPerGeneration: -1,
            canExportToMultipleFormats: true,
            canUseAdvancedAI: true,
            maxCustomCharacterTypes: -1,
            canAccessKnowledgeBase: true,
            canUseNovelExtension: true
          },
          // Flutter应用期望的limits字段
          limits: {
            maxChaptersPerNovel: (permissions?.maxChaptersPerNovel) || -1,
            maxKnowledgeDocuments: (permissions?.maxKnowledgeDocuments) || -1,
            canUseExtendedFeatures: (permissions?.canUseExtendedFeatures) !== false,
            maxNovelsPerDay: (permissions?.maxNovelsPerDay) || -1,
            maxWordsPerGeneration: (permissions?.maxWordsPerGeneration) || -1,
            canExportToMultipleFormats: (permissions?.canExportToMultipleFormats) !== false,
            canUseAdvancedAI: (permissions?.canUseAdvancedAI) !== false,
            maxCustomCharacterTypes: (permissions?.maxCustomCharacterTypes) || -1
          },
          isPopular: isPopular || false,
          isActive: isActive !== false, // 默认为true
          displayOnly: displayOnly || false, // 默认为false
          soldCount: 0,
          activeUsers: 0,
          sortOrder: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // 添加到CloudBase数据库
        const addResult = await db.collection('packages').add(newPackage);

        console.log('套餐添加成功:', packageId, 'CloudBase ID:', addResult.id);

        return createResponse(200, {
          success: true,
          data: { ...newPackage, _id: addResult.id },
          message: '套餐添加成功'
        });
      } catch (error) {
        console.error('添加套餐失败:', error);
        return createResponse(500, {
          success: false,
          message: '添加套餐失败: ' + error.message
        });
      }
    }

    // 更新套餐（管理员接口）
    if (routePath.startsWith('/packages/') && method === 'PUT') {
      try {
        const packageId = routePath.split('/packages/')[1];
        console.log('更新套餐（CloudBase数据库）:', packageId);

        const {
          name,
          price,
          unit,
          features,
          isPopular,
          isActive,
          description,
          durationDays,
          permissions,
          displayOnly
        } = body;

        // 从CloudBase数据库查找套餐
        const existingPackageResult = await db.collection('packages').where({
          id: packageId
        }).get();

        if (!existingPackageResult.data || existingPackageResult.data.length === 0) {
          return createResponse(404, {
            success: false,
            message: '套餐不存在'
          });
        }

        const existingPackage = existingPackageResult.data[0];

        // 检查套餐名称是否被其他套餐使用
        if (name && name !== existingPackage.name) {
          const duplicatePackageResult = await db.collection('packages').where({
            name: name,
            id: db.command.neq(packageId)
          }).get();

          if (duplicatePackageResult.data && duplicatePackageResult.data.length > 0) {
            return createResponse(400, {
              success: false,
              message: '套餐名称已被其他套餐使用'
            });
          }
        }

        // 准备更新数据
        const updateData = {
          updatedAt: new Date().toISOString()
        };

        if (name) updateData.name = name;
        if (description !== undefined) updateData.description = description;
        if (price !== undefined) updateData.price = parseFloat(price);
        if (unit) updateData.unit = unit;
        if (durationDays !== undefined) updateData.durationDays = durationDays;
        if (features) updateData.features = features;
        if (permissions) updateData.permissions = permissions;
        if (isPopular !== undefined) updateData.isPopular = isPopular;
        if (isActive !== undefined) updateData.isActive = isActive;
        if (displayOnly !== undefined) updateData.displayOnly = displayOnly;

        // 更新CloudBase数据库
        await db.collection('packages').doc(existingPackage._id).update(updateData);

        console.log('套餐更新成功:', packageId);

        return createResponse(200, {
          success: true,
          data: { ...existingPackage, ...updateData },
          message: '套餐更新成功'
        });
      } catch (error) {
        console.error('更新套餐失败:', error);
        return createResponse(500, {
          success: false,
          message: '更新套餐失败: ' + error.message
        });
      }
    }

    // 删除套餐（管理员接口）
    if (routePath.startsWith('/packages/') && method === 'DELETE') {
      try {
        const packageId = routePath.split('/packages/')[1];
        console.log('删除套餐（CloudBase数据库）:', packageId);

        // 从CloudBase数据库查找套餐
        const existingPackageResult = await db.collection('packages').where({
          id: packageId
        }).get();

        if (!existingPackageResult.data || existingPackageResult.data.length === 0) {
          return createResponse(404, {
            success: false,
            message: '套餐不存在'
          });
        }

        const existingPackage = existingPackageResult.data[0];

        // 检查是否有会员码关联到此套餐
        const relatedCodesResult = await db.collection('memberData').where({
          packageId: packageId
        }).get();

        if (relatedCodesResult.data && relatedCodesResult.data.length > 0) {
          return createResponse(400, {
            success: false,
            message: '该套餐下还有会员码，无法删除'
          });
        }

        // 从CloudBase数据库删除套餐
        await db.collection('packages').doc(existingPackage._id).remove();

        console.log('套餐删除成功:', packageId);

        return createResponse(200, {
          success: true,
          message: `套餐 ${existingPackage.name} 删除成功`
        });
      } catch (error) {
        console.error('删除套餐失败:', error);
        return createResponse(500, {
          success: false,
          message: '删除套餐失败: ' + error.message
        });
      }
    }

    // 获取用户订单
    if (routePath === '/orders/my' && method === 'GET') {
      try {
        // 验证Token
        const authHeader = headers.authorization || headers.Authorization;
        if (!authHeader) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '未提供认证令牌'
            })
          };
        }

        const token = authHeader.replace('Bearer ', '');
        const jwt = require('jsonwebtoken');
        const SECRET_KEY = 'novel-app-secret-key-2024';

        let userId;
        try {
          const decoded = jwt.verify(token, SECRET_KEY);
          userId = decoded.userId;
        } catch (error) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '无效的认证令牌'
            })
          };
        }

        console.log(`用户 ${userId} 请求获取订单列表`);

        // 从CloudBase数据库获取订单信息（暂时返回空数组，订单功能待实现）
        const userOrders = [];

        // 按创建时间倒序排列
        userOrders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        console.log(`✅ 找到用户 ${userId} 的 ${userOrders.length} 个订单`);

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: userOrders
          })
        };
      } catch (error) {
        console.error('获取用户订单失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取订单失败: ' + error.message
          })
        };
      }
    }

    // 会员码支付
    if (routePath === '/payment/member-code' && method === 'POST') {
      try {
        // 验证Token
        const authHeader = headers.authorization || headers.Authorization;
        if (!authHeader) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '未提供认证令牌'
            })
          };
        }

        const token = authHeader.replace('Bearer ', '');
        const jwt = require('jsonwebtoken');
        const SECRET_KEY = 'novel-app-secret-key-2024';

        let userId;
        try {
          const decoded = jwt.verify(token, SECRET_KEY);
          userId = decoded.userId;
        } catch (error) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '无效的认证令牌'
            })
          };
        }

        const { orderId, memberCode } = body;
        console.log(`用户 ${userId} 尝试使用会员码 ${memberCode} 支付订单 ${orderId}`);

        // 验证会员码
        const memberCodeResult = await db.collection('memberData').where({
          code: memberCode,
          isUsed: false
        }).get();

        if (memberCodeResult.data.length === 0) {
          return {
            statusCode: 400,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '会员码无效或已被使用'
            })
          };
        }

        const memberCodeData = memberCodeResult.data[0];

        // 检查会员码是否过期
        if (memberCodeData.expireAt && new Date(memberCodeData.expireAt) < new Date()) {
          return {
            statusCode: 400,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '会员码已过期'
            })
          };
        }

        // 获取套餐信息
        const packageResult = await db.collection('packages').doc(memberCodeData.packageId).get();
        if (!packageResult.data) {
          return {
            statusCode: 400,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '套餐不存在'
            })
          };
        }

        const packageData = packageResult.data;

        // 标记会员码为已使用
        await db.collection('memberData').doc(memberCodeData._id).update({
          isUsed: true,
          usedAt: new Date().toISOString(),
          usedBy: userId
        });

        // 更新用户会员信息
        const userResult = await db.collection('users').where({ id: userId }).get();
        if (userResult.data.length > 0) {
          const userData = userResult.data[0];
          const currentTime = new Date();
          let membershipExpireAt;

          if (packageData.durationDays === -1) {
            // 永久会员
            membershipExpireAt = null;
          } else {
            // 计算到期时间
            membershipExpireAt = new Date(currentTime.getTime() + packageData.durationDays * 24 * 60 * 60 * 1000).toISOString();
          }

          await db.collection('users').doc(userData._id).update({
            isMember: true,
            membershipType: packageData.id,
            membershipExpireAt: membershipExpireAt,
            currentPackage: {
              id: packageData.id,
              name: packageData.name,
              features: packageData.features || []
            },
            updatedAt: currentTime.toISOString()
          });
        }

        console.log(`✅ 用户 ${userId} 成功使用会员码 ${memberCode}`);

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '会员码使用成功',
            data: {
              packageId: packageData.id,
              packageName: packageData.name,
              membershipExpireAt: packageData.durationDays === -1 ? null : new Date(new Date().getTime() + packageData.durationDays * 24 * 60 * 60 * 1000).toISOString()
            }
          })
        };
      } catch (error) {
        console.error('会员码支付失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '会员码支付失败: ' + error.message
          })
        };
      }
    }

    // 创建订单
    if (routePath === '/orders/create' && method === 'POST') {
      try {
        // 验证Token
        const authHeader = headers.authorization || headers.Authorization;
        if (!authHeader) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '未提供认证令牌'
            })
          };
        }

        const token = authHeader.replace('Bearer ', '');
        const jwt = require('jsonwebtoken');
        const SECRET_KEY = 'novel-app-secret-key-2024';

        let userId;
        try {
          const decoded = jwt.verify(token, SECRET_KEY);
          userId = decoded.userId;
        } catch (error) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '无效的认证令牌'
            })
          };
        }

        const { packageId } = body;
        console.log(`用户 ${userId} 创建订单，套餐ID: ${packageId}`);

        // 如果是会员码临时订单，直接创建
        if (packageId === 'member_code_package') {
          const orderId = `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

          return {
            statusCode: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: true,
              data: {
                id: orderId,
                packageId: packageId,
                userId: userId,
                status: 'pending',
                createdAt: new Date().toISOString()
              }
            })
          };
        }

        // 获取套餐信息
        const packageResult = await db.collection('packages').doc(packageId).get();
        if (!packageResult.data) {
          return {
            statusCode: 400,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '套餐不存在'
            })
          };
        }

        const packageData = packageResult.data;
        const orderId = `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // 创建订单（暂时不存储到数据库，只返回订单信息）
        const orderData = {
          id: orderId,
          userId: userId,
          packageId: packageId,
          packageName: packageData.name,
          amount: packageData.price,
          status: 'pending',
          createdAt: new Date().toISOString()
        };

        console.log(`✅ 用户 ${userId} 创建订单成功: ${orderId}`);

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: orderData
          })
        };
      } catch (error) {
        console.error('创建订单失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '创建订单失败: ' + error.message
          })
        };
      }
    }

    // 管理员登录
    if (routePath === '/admin/login' && method === 'POST') {
      try {
        const { username, password } = body;

        console.log('管理员登录尝试:', username);

        // 简单的管理员验证（生产环境应该使用更安全的方式）
        const adminCredentials = {
          'admin': 'admin123',
          'root': 'root123',
          'dznovel': 'dznovel2024'
        };

        if (adminCredentials[username] && adminCredentials[username] === password) {
          // 生成管理员Token
          const jwt = require('jsonwebtoken');
          const SECRET_KEY = 'novel-app-secret-key-2024';

          const adminToken = jwt.sign(
            {
              userId: 'admin_' + username,
              username: username,
              role: 'admin',
              isAdmin: true
            },
            SECRET_KEY,
            { expiresIn: '24h' }
          );

          console.log('✅ 管理员登录成功:', username);

          return {
            statusCode: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: true,
              message: '管理员登录成功',
              data: {
                token: adminToken,
                user: {
                  id: 'admin_' + username,
                  username: username,
                  role: 'admin'
                }
              }
            })
          };
        } else {
          console.log('❌ 管理员登录失败: 用户名或密码错误');
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '用户名或密码错误'
            })
          };
        }
      } catch (error) {
        console.error('管理员登录失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '管理员登录失败: ' + error.message
          })
        };
      }
    }

    // 验证管理员token
    if (routePath === '/admin/verify' && method === 'GET') {
      try {
        const authHeader = event.headers?.authorization || event.headers?.Authorization;
        if (!authHeader) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '未提供认证令牌'
            })
          };
        }

        const token = authHeader.replace('Bearer ', '');
        const jwt = require('jsonwebtoken');
        const SECRET_KEY = 'novel-app-secret-key-2024';

        try {
          const decoded = jwt.verify(token, SECRET_KEY);
          if (decoded.isAdmin) {
            return {
              statusCode: 200,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: true,
                data: {
                  id: decoded.userId,
                  username: decoded.username,
                  role: decoded.role
                }
              })
            };
          } else {
            throw new Error('非管理员用户');
          }
        } catch (jwtError) {
          return {
            statusCode: 401,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '无效的认证令牌'
            })
          };
        }
      } catch (error) {
        console.error('验证管理员token异常:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '验证失败: ' + error.message
          })
        };
      }
    }

    // 获取系统状态
    if (routePath === '/system/status' && method === 'GET') {
      try {
        // 获取系统状态信息
        const systemStatus = {
          apiStatus: 'healthy',
          databaseStatus: 'healthy',
          syncServiceStatus: 'healthy',
          timestamp: new Date().toISOString(),
          version: '4.3.12',
          environment: process.env.NODE_ENV || 'production'
        };

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: systemStatus
          })
        };
      } catch (error) {
        console.error('获取系统状态失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取系统状态失败: ' + error.message
          })
        };
      }
    }

    // 获取仪表板数据
    if (routePath === '/dashboard' && method === 'GET') {
      try {
        console.log('获取仪表板数据（从CloudBase数据库）');

        // 从CloudBase数据库获取统计数据
        const usersResult = await db.collection('users').get();
        const users = usersResult.data || [];

        const memberCodesResult = await db.collection('memberData').get();
        const memberCodes = memberCodesResult.data || [];

        // 计算统计数据
        const totalUsers = users.length;
        const totalMembers = users.filter(u => u.isMember).length;
        const totalNovels = users.reduce((total, user) => {
          return total + (user.syncData && user.syncData.novels ? user.syncData.novels.length : 0);
        }, 0);
        const totalMemberCodes = memberCodes.length;

        // 计算同步相关数据
        const todayStart = new Date();
        todayStart.setHours(0, 0, 0, 0);
        const todaySync = users.filter(user => {
          if (!user.syncTimestamp) return false;
          const syncDate = new Date(user.syncTimestamp);
          return syncDate >= todayStart;
        }).length;

        const totalSyncRecords = users.filter(user => user.syncData).length;

        // 获取最新注册用户（最近5个）
        const recentUsers = users
          .sort((a, b) => new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime())
          .slice(0, 5)
          .map(user => ({
            id: user._id,
            username: user.username || '未知用户',
            phoneNumber: user.phoneNumber || '未绑定',
            isMember: user.isMember || false,
            createdAt: user.createdAt || new Date().toISOString()
          }));

        // 获取热门小说（从用户同步数据中提取）
        const allNovels = [];
        users.forEach(user => {
          if (user.syncData && user.syncData.novels) {
            user.syncData.novels.forEach(novel => {
              allNovels.push({
                id: novel.id || Math.random().toString(36).substr(2, 9),
                title: novel.title || '未命名小说',
                genre: novel.genre || '未分类',
                wordCount: novel.wordCount || 0,
                createdAt: novel.createdAt || user.syncTimestamp || new Date().toISOString(),
                author: user.username || '匿名作者'
              });
            });
          }
        });

        // 按字数排序，取前5个
        const popularNovels = allNovels
          .sort((a, b) => (b.wordCount || 0) - (a.wordCount || 0))
          .slice(0, 5);

        const dashboardData = {
          stats: {
            totalUsers,
            totalNovels,
            memberUsers: totalMembers, // 会员用户数
            todaySync, // 今日同步数量
            totalMemberCodes, // 会员码总数
            totalSyncRecords, // 总同步记录数
            membershipRate: totalUsers > 0 ? ((totalMembers / totalUsers) * 100).toFixed(1) : '0'
          },
          recentActivity: {
            newUsersToday: Math.floor(Math.random() * 10),
            newMembersToday: Math.floor(Math.random() * 5),
            syncRequestsToday: todaySync
          },
          systemStatus: {
            apiStatus: 'healthy',
            databaseStatus: 'healthy',
            syncServiceStatus: 'healthy'
          },
          recentUsers: recentUsers, // 最新用户列表
          popularNovels: popularNovels // 热门小说列表
        };

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: dashboardData
          })
        };
      } catch (error) {
        console.error('获取仪表板数据失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取仪表板数据失败: ' + error.message
          })
        };
      }
    }

    // 获取用户列表
    if (routePath === '/users' && method === 'GET') {
      try {
        console.log('获取用户列表（从CloudBase数据库）');

        // 从CloudBase数据库获取用户数据
        const usersResult = await db.collection('users').get();
        let users = usersResult.data || [];

        console.log('从CloudBase获取到用户数量:', users.length);

        // 根据会员类型过滤
        const memberType = query.memberType;
        if (memberType) {
          if (memberType === 'none') {
            users = users.filter(u => !u.isMember);
          } else if (memberType === 'monthly' || memberType === 'permanent') {
            users = users.filter(u => u.isMember);
          }
        }

        // 根据关键词搜索
        const keyword = query.keyword;
        if (keyword) {
          users = users.filter(u =>
            (u.username && u.username.includes(keyword)) ||
            (u.phoneNumber && u.phoneNumber.includes(keyword)) ||
            (u.email && u.email.includes(keyword))
          );
        }

        // 过滤敏感信息并格式化数据
        const safeUsers = users.map(user => {
          // 确定会员类型
          let memberType = 'none';
          if (user.isMember) {
            if (user.isPermanentMember) {
              memberType = 'permanent';
            } else if (user.membershipType) {
              memberType = user.membershipType;
            } else {
              memberType = 'monthly'; // 默认为月会员
            }
          }

          return {
            // 将id和username放在最前面
            id: user.id,
            username: user.username,
            // 其他字段
            email: user.email,
            phone: user.phoneNumber,
            phoneNumber: user.phoneNumber, // 前端期望的字段名
            isMember: user.isMember || false,
            isPermanentMember: user.isPermanentMember || false,
            membershipExpiry: user.memberExpireTime,
            memberType: memberType,
            createdAt: user.createdAt,
            lastLoginAt: user.lastLoginAt,
            isDataSyncEnabled: user.isDataSyncEnabled || true,
            syncCount: user.syncData ? Object.keys(user.syncData).length : 0,
            novelCount: user.syncData && user.syncData.novels ? user.syncData.novels.length : 0
            // 注意：不包含_id字段
          };
        });

        // 分页处理
        const page = parseInt(query.page) || 1;
        const size = parseInt(query.size) || 10;
        const startIndex = (page - 1) * size;
        const endIndex = startIndex + size;

        const paginatedUsers = safeUsers.slice(startIndex, endIndex);

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              users: paginatedUsers,
              total: safeUsers.length
            },
            pagination: {
              page,
              size,
              total: safeUsers.length,
              totalPages: Math.ceil(safeUsers.length / size)
            }
          })
        };
      } catch (error) {
        console.error('获取用户列表失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取用户列表失败: ' + error.message
          })
        };
      }
    }

    // 获取小说列表
    if (routePath === '/novels' && method === 'GET') {
      try {
        console.log('获取小说列表（从CloudBase数据库）');

        // 从CloudBase数据库获取用户数据
        const usersResult = await db.collection('users').get();
        const users = usersResult.data || [];

        console.log('从CloudBase获取到用户数量:', users.length);

        // 收集所有小说
        let allNovels = [];
        users.forEach(user => {
          if (user.syncData && user.syncData.novels) {
            const userNovels = user.syncData.novels.map(novel => ({
              id: novel.id || `novel_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              title: novel.title || '未命名小说',
              author: user.username || '匿名作者',
              userId: user.userId || user.id,
              genre: novel.genre || '其他',
              wordCount: novel.wordCount || (novel.content ? novel.content.length : 0),
              chapterCount: novel.chapterCount || (novel.chapters ? novel.chapters.length : 0),
              status: novel.status || 'ongoing',
              qualityScore: novel.qualityScore || Math.floor(Math.random() * 3) + 3, // 3-5分
              createdAt: novel.createdAt || user.syncTimestamp || new Date().toISOString(),
              updatedAt: novel.updatedAt || user.syncTimestamp || new Date().toISOString(),
              syncedAt: user.syncTimestamp,
              description: novel.description || novel.summary || '暂无简介'
            }));
            allNovels = allNovels.concat(userNovels);
          }
        });

        console.log('收集到小说总数:', allNovels.length);

        // 应用搜索和筛选
        let filteredNovels = allNovels;

        // 关键词搜索
        if (query.keyword) {
          const keyword = query.keyword.toLowerCase();
          filteredNovels = filteredNovels.filter(novel =>
            novel.title.toLowerCase().includes(keyword) ||
            novel.author.toLowerCase().includes(keyword)
          );
        }

        // 类型筛选
        if (query.genre) {
          filteredNovels = filteredNovels.filter(novel => novel.genre === query.genre);
        }

        // 状态筛选
        if (query.status) {
          filteredNovels = filteredNovels.filter(novel => novel.status === query.status);
        }

        // 排序（默认按更新时间倒序）
        filteredNovels.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());

        console.log('筛选后小说数量:', filteredNovels.length);

        // 分页处理
        const page = parseInt(query.page) || 1;
        const size = parseInt(query.size) || 10;
        const startIndex = (page - 1) * size;
        const endIndex = startIndex + size;

        const paginatedNovels = filteredNovels.slice(startIndex, endIndex);

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              novels: paginatedNovels,
              total: filteredNovels.length,
              page: page,
              size: size,
              totalPages: Math.ceil(filteredNovels.length / size)
            }
          })
        };
      } catch (error) {
        console.error('获取小说列表失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取小说列表失败: ' + error.message
          })
        };
      }
    }

    // 获取单个小说详情
    if (routePath.startsWith('/novels/') && routePath !== '/novels/stats' && method === 'GET') {
      try {
        console.log('获取小说详情');

        const authHeader = headers.authorization || headers.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return createResponse(401, {
            success: false,
            message: '未提供认证令牌'
          });
        }

        const novelId = routePath.split('/novels/')[1];
        if (!novelId) {
          return createResponse(400, {
            success: false,
            message: '小说ID不能为空'
          });
        }

        // 从CloudBase数据库获取用户同步数据
        const usersResult = await db.collection('users').get();
        const users = usersResult.data || [];

        // 查找指定的小说
        let foundNovel = null;
        let novelOwner = null;

        for (const user of users) {
          if (user.syncData && user.syncData.novels) {
            const novel = user.syncData.novels.find(n => n.id === novelId);
            if (novel) {
              foundNovel = novel;
              novelOwner = user;
              break;
            }
          }
        }

        if (!foundNovel) {
          return createResponse(404, {
            success: false,
            message: '小说不存在'
          });
        }

        // 构建详细的小说信息
        const novelDetail = {
          id: foundNovel.id || novelId,
          title: foundNovel.title || '未命名小说',
          author: novelOwner.username || '匿名作者',
          userId: novelOwner.userId || novelOwner.id,
          genre: foundNovel.genre || '其他',
          wordCount: foundNovel.wordCount || (foundNovel.content ? foundNovel.content.length : 0),
          chapterCount: foundNovel.chapterCount || (foundNovel.chapters ? foundNovel.chapters.length : 0),
          status: foundNovel.status || 'ongoing',
          qualityScore: foundNovel.qualityScore || Math.floor(Math.random() * 3) + 3,
          createdAt: foundNovel.createdAt || novelOwner.syncTimestamp || new Date().toISOString(),
          updatedAt: foundNovel.updatedAt || novelOwner.syncTimestamp || new Date().toISOString(),
          syncedAt: novelOwner.syncTimestamp,
          description: foundNovel.description || foundNovel.summary || '暂无简介',
          content: foundNovel.content || '',
          chapters: foundNovel.chapters || [],
          tags: foundNovel.tags || [],
          language: foundNovel.language || 'zh-CN',
          isPublished: foundNovel.isPublished || false
        };

        return createResponse(200, {
          success: true,
          data: novelDetail
        });

      } catch (error) {
        console.error('获取小说详情失败:', error);
        return createResponse(500, {
          success: false,
          message: '获取小说详情失败'
        });
      }
    }

    // 删除单个小说
    if (routePath.startsWith('/novels/') && routePath !== '/novels/stats' && method === 'DELETE') {
      try {
        console.log('删除小说');

        const authHeader = headers.authorization || headers.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return createResponse(401, {
            success: false,
            message: '未提供认证令牌'
          });
        }

        const novelId = routePath.split('/novels/')[1];
        if (!novelId) {
          return createResponse(400, {
            success: false,
            message: '小说ID不能为空'
          });
        }

        // 从CloudBase数据库获取用户同步数据
        const usersResult = await db.collection('users').get();
        const users = usersResult.data || [];

        // 查找并删除指定的小说
        let novelFound = false;
        let updatedUser = null;

        for (const user of users) {
          if (user.syncData && user.syncData.novels) {
            const novelIndex = user.syncData.novels.findIndex(n => n.id === novelId);
            if (novelIndex !== -1) {
              // 找到小说，从数组中删除
              user.syncData.novels.splice(novelIndex, 1);
              novelFound = true;
              updatedUser = user;
              break;
            }
          }
        }

        if (!novelFound) {
          return createResponse(404, {
            success: false,
            message: '小说不存在'
          });
        }

        // 更新数据库中的用户数据
        await db.collection('users').doc(updatedUser._id).update({
          syncData: updatedUser.syncData,
          updatedAt: new Date().toISOString()
        });

        return createResponse(200, {
          success: true,
          message: '小说删除成功'
        });

      } catch (error) {
        console.error('删除小说失败:', error);
        return createResponse(500, {
          success: false,
          message: '删除小说失败'
        });
      }
    }

    // 批量删除小说
    if (routePath === '/novels/batch-delete' && method === 'POST') {
      try {
        console.log('批量删除小说');

        const authHeader = headers.authorization || headers.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return createResponse(401, {
            success: false,
            message: '未提供认证令牌'
          });
        }

        const body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
        const { novelIds } = body;

        if (!novelIds || !Array.isArray(novelIds) || novelIds.length === 0) {
          return createResponse(400, {
            success: false,
            message: '请提供要删除的小说ID列表'
          });
        }

        // 从CloudBase数据库获取用户同步数据
        const usersResult = await db.collection('users').get();
        const users = usersResult.data || [];

        let deletedCount = 0;
        const updatedUsers = [];

        // 遍历所有用户，查找并删除指定的小说
        for (const user of users) {
          if (user.syncData && user.syncData.novels) {
            const originalLength = user.syncData.novels.length;

            // 过滤掉要删除的小说
            user.syncData.novels = user.syncData.novels.filter(novel =>
              !novelIds.includes(novel.id)
            );

            const deletedFromThisUser = originalLength - user.syncData.novels.length;
            if (deletedFromThisUser > 0) {
              deletedCount += deletedFromThisUser;
              updatedUsers.push(user);
            }
          }
        }

        if (deletedCount === 0) {
          return createResponse(404, {
            success: false,
            message: '未找到要删除的小说'
          });
        }

        // 批量更新数据库中的用户数据
        const updatePromises = updatedUsers.map(user =>
          db.collection('users').doc(user._id).update({
            syncData: user.syncData,
            updatedAt: new Date().toISOString()
          })
        );

        await Promise.all(updatePromises);

        return createResponse(200, {
          success: true,
          message: `成功删除 ${deletedCount} 部小说`,
          data: {
            deletedCount: deletedCount
          }
        });

      } catch (error) {
        console.error('批量删除小说失败:', error);
        return createResponse(500, {
          success: false,
          message: '批量删除小说失败'
        });
      }
    }

    // 获取小说统计
    if (routePath === '/novels/stats' && method === 'GET') {
      try {
        console.log('获取小说统计');

        // 从CloudBase数据库获取用户同步数据来统计小说信息
        const usersResult = await db.collection('users').get();
        const users = usersResult.data || [];

        let totalNovels = 0;
        let totalWords = 0;
        let totalChapters = 0;
        let todayCreated = 0;

        const today = new Date();
        today.setHours(0, 0, 0, 0);

        users.forEach(user => {
          if (user.syncData && user.syncData.novels) {
            totalNovels += user.syncData.novels.length;
            user.syncData.novels.forEach(novel => {
              // 计算字数
              if (novel.wordCount) {
                totalWords += novel.wordCount;
              } else if (novel.content) {
                totalWords += novel.content.length;
              }

              // 计算章节数
              if (novel.chapterCount) {
                totalChapters += novel.chapterCount;
              } else if (novel.chapters) {
                totalChapters += novel.chapters.length;
              }

              // 计算今日新增
              if (novel.createdAt) {
                const createdDate = new Date(novel.createdAt);
                if (createdDate >= today) {
                  todayCreated++;
                }
              }
            });
          }
        });

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              total: totalNovels,
              totalWords: totalWords,
              todayCreated: todayCreated,
              avgWords: totalNovels > 0 ? Math.round(totalWords / totalNovels) : 0
            }
          })
        };
      } catch (error) {
        console.error('获取小说统计失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取小说统计失败: ' + error.message
          })
        };
      }
    }

    // 获取会员统计
    if (routePath === '/members/stats' && method === 'GET') {
      try {
        console.log('获取会员统计');

        // 从CloudBase数据库获取用户数据
        const usersResult = await db.collection('users').get();
        const users = usersResult.data || [];

        const totalUsers = users.length;
        const totalMembers = users.filter(u => u.isMember).length;
        const activeMembers = users.filter(u => u.isMember && new Date(u.membershipExpiry) > new Date()).length;

        // 按会员类型分类统计
        const permanentMembers = users.filter(u => u.isMember && (u.isPermanentMember || u.membershipType === 'permanent')).length;
        const monthlyMembers = users.filter(u => u.isMember && !u.isPermanentMember && u.membershipType !== 'permanent').length;
        const conversionRate = totalUsers > 0 ? ((totalMembers / totalUsers) * 100).toFixed(1) : '0';

        return createResponse(200, {
          success: true,
          data: {
            totalUsers,
            totalMembers,
            activeMembers,
            permanentMembers,
            monthlyMembers,
            conversionRate: parseFloat(conversionRate)
          }
        });
      } catch (error) {
        console.error('获取会员统计失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取会员统计失败: ' + error.message
          })
        };
      }
    }

    // 获取会员码列表
    if (routePath === '/member-codes' && method === 'GET') {
      try {
        console.log('获取会员码列表');

        // 使用CloudBase数据库获取会员码
        const memberCodesResult = await db.collection('memberData').get();
        let memberCodes = memberCodesResult.data || [];

        console.log('从CloudBase获取到会员码数量:', memberCodes.length);

        // 为每个会员码添加id字段（基于code）
        memberCodes = memberCodes.map(code => ({
          ...code,
          id: code.code // 使用code作为id
        }));

        // 分页处理
        const page = parseInt(query.page) || 1;
        const size = parseInt(query.size) || 10;
        const startIndex = (page - 1) * size;
        const endIndex = startIndex + size;

        const paginatedCodes = memberCodes.slice(startIndex, endIndex);

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              memberCodes: paginatedCodes,
              total: memberCodes.length
            },
            pagination: {
              page,
              size,
              total: memberCodes.length,
              totalPages: Math.ceil(memberCodes.length / size)
            }
          })
        };
      } catch (error) {
        console.error('获取会员码列表失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取会员码列表失败: ' + error.message
          })
        };
      }
    }

    // 获取同步统计
    if (routePath === '/sync/stats' && method === 'GET') {
      try {
        console.log('获取同步统计');

        // 从CloudBase数据库获取用户数据来统计同步信息
        const usersResult = await db.collection('users').get();
        const users = usersResult.data || [];

        const totalSyncRecords = users.filter(user => user.syncData).length;
        const todayStart = new Date();
        todayStart.setHours(0, 0, 0, 0);

        const todaySyncs = users.filter(user => {
          if (!user.syncTimestamp) return false;
          const syncDate = new Date(user.syncTimestamp);
          return syncDate >= todayStart;
        }).length;

        // 计算总数据大小
        let totalDataSize = 0;
        users.forEach(user => {
          if (user.syncData) {
            totalDataSize += JSON.stringify(user.syncData).length;
          }
        });

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              totalSyncs: totalSyncRecords,
              successRate: totalSyncRecords > 0 ? 90 : 0, // 模拟90%成功率
              todayCount: todaySyncs,
              totalDataSize: totalDataSize,
              queueCount: Math.floor(Math.random() * 5), // 模拟队列任务数
              directUploadCount: Math.floor(totalSyncRecords * 0.6), // 60%使用直传
              failedCount: Math.floor(totalSyncRecords * 0.1), // 10%失败率
              avgDuration: totalSyncRecords > 0 ? `${(Math.random() * 3 + 1).toFixed(1)}s` : '0s'
            }
          })
        };
      } catch (error) {
        console.error('获取同步统计失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '获取同步统计失败: ' + error.message
          })
        };
      }
    }

    // 手动触发用户同步
    if (routePath === '/sync/trigger' && method === 'POST') {
      try {
        console.log('手动触发用户同步');

        const authHeader = headers.authorization || headers.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return createResponse(401, {
            success: false,
            message: '未提供认证令牌'
          });
        }

        const body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
        const { userId, syncContent, syncMethod = 'auto', enableChunked = true } = body;

        if (!userId || !syncContent || !Array.isArray(syncContent)) {
          return createResponse(400, {
            success: false,
            message: '参数错误：缺少用户ID或同步内容'
          });
        }

        // 查找用户
        const userResult = await db.collection('users').where({
          userId: userId
        }).get();

        if (!userResult.data || userResult.data.length === 0) {
          return createResponse(404, {
            success: false,
            message: '用户不存在'
          });
        }

        // 创建同步任务记录
        const syncRecord = {
          id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          userId: userId,
          username: userResult.data[0].username || '未知用户',
          syncContent: syncContent,
          syncMethod: syncMethod,
          status: 'syncing',
          dataSize: 0,
          duration: null,
          timestamp: new Date().toISOString(),
          error: null,
          details: `管理员手动触发同步，内容：${syncContent.join(', ')}`,
          enableChunked: enableChunked
        };

        // 这里应该实际触发同步逻辑，现在先模拟
        // 在实际实现中，这里会调用同步服务

        return createResponse(200, {
          success: true,
          message: '同步任务已创建',
          data: {
            syncId: syncRecord.id,
            status: 'syncing'
          }
        });

      } catch (error) {
        console.error('触发同步失败:', error);
        return createResponse(500, {
          success: false,
          message: '触发同步失败'
        });
      }
    }

    // 获取同步队列
    if (routePath === '/sync/queue' && method === 'GET') {
      try {
        console.log('获取同步队列');

        const authHeader = headers.authorization || headers.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return createResponse(401, {
            success: false,
            message: '未提供认证令牌'
          });
        }

        // 模拟队列数据，实际应该从队列系统获取
        const queueTasks = [
          {
            id: 'task_001',
            userId: 'user_001',
            type: 'novels',
            status: 'pending',
            createdAt: new Date().toISOString()
          },
          {
            id: 'task_002',
            userId: 'user_002',
            type: 'characters',
            status: 'running',
            createdAt: new Date(Date.now() - 300000).toISOString()
          }
        ];

        return createResponse(200, {
          success: true,
          data: queueTasks
        });

      } catch (error) {
        console.error('获取同步队列失败:', error);
        return createResponse(500, {
          success: false,
          message: '获取同步队列失败'
        });
      }
    }

    // 获取同步配置
    if (routePath === '/sync/config' && method === 'GET') {
      try {
        console.log('获取同步配置');

        const authHeader = headers.authorization || headers.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return createResponse(401, {
            success: false,
            message: '未提供认证令牌'
          });
        }

        // 返回默认同步配置
        const defaultConfig = {
          defaultSyncMethod: 'auto',
          chunkThreshold: 10,
          maxRetries: 3,
          timeout: 60,
          autoClean: true,
          enableCompression: true,
          concurrentTasks: 2
        };

        return createResponse(200, {
          success: true,
          data: defaultConfig
        });

      } catch (error) {
        console.error('获取同步配置失败:', error);
        return createResponse(500, {
          success: false,
          message: '获取同步配置失败'
        });
      }
    }

    // 更新同步配置
    if (routePath === '/sync/config' && method === 'PUT') {
      try {
        console.log('更新同步配置');

        const authHeader = headers.authorization || headers.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return createResponse(401, {
            success: false,
            message: '未提供认证令牌'
          });
        }

        const body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;

        // 这里应该保存配置到数据库，现在先模拟成功
        console.log('保存同步配置:', body);

        return createResponse(200, {
          success: true,
          message: '同步配置更新成功'
        });

      } catch (error) {
        console.error('更新同步配置失败:', error);
        return createResponse(500, {
          success: false,
          message: '更新同步配置失败'
        });
      }
    }

    // 清理同步记录
    if (routePath === '/sync/records/clean' && method === 'DELETE') {
      try {
        console.log('清理同步记录');

        const authHeader = headers.authorization || headers.Authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return createResponse(401, {
            success: false,
            message: '未提供认证令牌'
          });
        }

        const query = event.queryStringParameters || {};
        const { beforeDate, status } = query;

        // 这里应该实际清理数据库记录，现在先模拟
        let cleanedCount = 0;

        if (beforeDate) {
          cleanedCount = Math.floor(Math.random() * 50) + 10;
        } else if (status) {
          cleanedCount = Math.floor(Math.random() * 20) + 5;
        } else {
          cleanedCount = Math.floor(Math.random() * 100) + 50;
        }

        return createResponse(200, {
          success: true,
          message: `成功清理 ${cleanedCount} 条记录`,
          data: {
            cleanedCount: cleanedCount
          }
        });

      } catch (error) {
        console.error('清理同步记录失败:', error);
        return createResponse(500, {
          success: false,
          message: '清理同步记录失败'
        });
      }
    }



    // 测试路由匹配
    if (routePath === '/users/test-route' && method === 'GET') {
      return createResponse(200, {
        success: true,
        message: '测试路由匹配成功',
        routePath: routePath,
        method: method
      });
    }

    // 获取单个用户详情
    if (routePath.match(/^\/users\/(.+)$/) && method === 'GET') {
      try {
        const userId = routePath.match(/^\/users\/(.+)$/)[1];
        console.log('=== 获取用户详情 ===');
        console.log('用户ID:', userId);
        console.log('路由路径:', routePath);

        // 从CloudBase数据库获取用户详情
        // 先尝试通过文档ID查询
        console.log('尝试通过文档ID查询...');
        let userResult = await db.collection('users').doc(userId).get();
        console.log('文档ID查询结果:', userResult.data ? '找到数据' : '未找到数据');

        // 如果通过文档ID找不到，尝试通过自定义id字段查询
        if (!userResult.data) {
          console.log('尝试通过自定义id字段查询...');
          const queryResult = await db.collection('users').where({
            id: userId
          }).get();
          console.log('自定义id查询结果:', queryResult.data ? `找到${queryResult.data.length}条数据` : '未找到数据');

          if (queryResult.data && queryResult.data.length > 0) {
            userResult = { data: queryResult.data[0] };
            console.log('使用查询结果:', userResult.data.id);
          }
        }

        if (userResult.data) {
          console.log('返回用户数据:', userResult.data.id);
          return createResponse(200, {
            success: true,
            data: userResult.data
          });
        } else {
          console.log('用户不存在，返回404');
          return createResponse(404, {
            success: false,
            message: '用户不存在'
          });
        }
      } catch (error) {
        console.error('获取用户详情失败:', error);
        return createResponse(500, {
          success: false,
          message: '获取用户详情失败: ' + error.message
        });
      }
    }

    // 删除用户
    if (routePath.match(/^\/users\/(.+)$/) && method === 'DELETE') {
      try {
        const userId = routePath.match(/^\/users\/(.+)$/)[1];
        console.log('删除用户:', userId);

        // 从CloudBase数据库删除用户
        // 先尝试通过文档ID删除
        let deleteResult = await db.collection('users').doc(userId).remove();

        // 如果通过文档ID删除失败，尝试通过自定义id字段查询并删除
        if (deleteResult.deleted === 0) {
          const queryResult = await db.collection('users').where({
            id: userId
          }).get();

          if (queryResult.data && queryResult.data.length > 0) {
            const docId = queryResult.data[0]._id;
            deleteResult = await db.collection('users').doc(docId).remove();
          }
        }

        if (deleteResult.deleted > 0) {
          return {
            statusCode: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: true,
              message: '用户删除成功'
            })
          };
        } else {
          return {
            statusCode: 404,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '用户不存在'
            })
          };
        }
      } catch (error) {
        console.error('删除用户失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '删除用户失败: ' + error.message
          })
        };
      }
    }

    // 批量删除用户
    if (routePath === '/users/batch-delete' && method === 'POST') {
      try {
        const { userIds } = body;
        console.log('批量删除用户:', userIds);

        // 从CloudBase数据库批量删除用户
        let deletedCount = 0;

        for (const userId of userIds) {
          try {
            const deleteResult = await db.collection('users').doc(userId).remove();
            if (deleteResult.deleted > 0) {
              deletedCount++;
            }
          } catch (error) {
            console.error(`删除用户 ${userId} 失败:`, error);
          }
        }

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: `成功删除 ${deletedCount} 个用户`
          })
        };
      } catch (error) {
        console.error('批量删除用户失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '批量删除用户失败: ' + error.message
          })
        };
      }
    }

    // 编辑用户
    if (routePath.match(/^\/users\/(.+)$/) && method === 'PUT') {
      try {
        const userId = routePath.match(/^\/users\/(.+)$/)[1];
        const { username, email, phoneNumber, memberType } = body;

        console.log('编辑用户:', userId, body);

        // 从CloudBase数据库更新用户信息
        const updateData = {
          updatedAt: new Date().toISOString()
        };

        if (username) updateData.username = username;
        if (email) updateData.email = email;
        if (phoneNumber) updateData.phone = phoneNumber;

        // 处理会员类型设置
        if (memberType !== undefined) {
          updateData.isMember = memberType !== 'none';

          if (memberType === 'permanent') {
            updateData.isPermanentMember = true;
            updateData.membershipType = 'permanent';
            // 永久会员不设置过期时间
            updateData.memberExpireTime = null;
          } else if (memberType === 'monthly') {
            updateData.isPermanentMember = false;
            updateData.membershipType = 'monthly';
            // 设置月会员过期时间（30天后）
            const expireDate = new Date();
            expireDate.setDate(expireDate.getDate() + 30);
            updateData.memberExpireTime = expireDate.toISOString();
          } else {
            // memberType === 'none'，取消会员
            updateData.isPermanentMember = false;
            updateData.membershipType = null;
            updateData.memberExpireTime = null;
          }
        }

        // 先尝试通过文档ID更新
        let updateResult = await db.collection('users').doc(userId).update(updateData);

        // 如果通过文档ID更新失败，尝试通过自定义id字段查询并更新
        if (updateResult.updated === 0) {
          const queryResult = await db.collection('users').where({
            id: userId
          }).get();

          if (queryResult.data && queryResult.data.length > 0) {
            const docId = queryResult.data[0]._id;
            updateResult = await db.collection('users').doc(docId).update(updateData);
          }
        }

        if (updateResult.updated > 0) {
          return {
            statusCode: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: true,
              message: '用户信息更新成功'
            })
          };
        } else {
          return {
            statusCode: 404,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '用户不存在'
            })
          };
        }
      } catch (error) {
        console.error('编辑用户失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '编辑用户失败: ' + error.message
          })
        };
      }
    }

    // 生成会员码
    if (routePath === '/member-codes/generate' && method === 'POST') {
      try {
        const { packageId, count, customCodes } = body;
        console.log('生成会员码:', { packageId, count, customCodes });

        const newCodes = [];

        if (customCodes && customCodes.length > 0) {
          // 添加自定义会员码
          customCodes.forEach(code => {
            newCodes.push({
              code: code,
              packageId: packageId,
              status: 'unused',
              createdAt: new Date().toISOString(),
              expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString() // 1年后过期
            });
          });
        } else if (count) {
          // 生成指定数量的会员码
          for (let i = 0; i < count; i++) {
            newCodes.push({
              code: generateMemberCode('VIP', 8),
              packageId: packageId,
              status: 'unused',
              createdAt: new Date().toISOString(),
              expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
            });
          }
        }

        // 将新会员码添加到CloudBase数据库
        if (newCodes.length > 0) {
          await db.collection('memberData').add(newCodes);
        }

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: `成功生成 ${newCodes.length} 个会员码`,
            data: newCodes
          })
        };
      } catch (error) {
        console.error('生成会员码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '生成会员码失败: ' + error.message
          })
        };
      }
    }

    // 删除会员码
    if (routePath.match(/^\/member-codes\/(.+)$/) && method === 'DELETE') {
      try {
        const codeValue = routePath.match(/^\/member-codes\/(.+)$/)[1];
        console.log('删除会员码:', codeValue);

        // 使用CloudBase数据库删除会员码
        console.log('🔍 查询会员码:', codeValue);
        const queryResult = await db.collection('memberData').where({ code: codeValue }).get();
        console.log('📊 查询结果:', queryResult.data.length, '个结果');

        if (queryResult.data.length > 0) {
          console.log('✅ 找到会员码，执行删除');
          const deleteResult = await db.collection('memberData').where({ code: codeValue }).remove();
          console.log('🗑️ 删除结果:', deleteResult);

          return {
            statusCode: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: true,
              message: '会员码删除成功'
            })
          };
        } else {
          // 调试：查看数据库中所有数据
          const allData = await db.collection('memberData').get();
          console.log('❌ 会员码不存在，数据库中所有会员码:', allData.data.map(item => item.code));

          return {
            statusCode: 404,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: `会员码不存在: ${codeValue}，数据库中有: ${allData.data.map(item => item.code).join(', ')}`
            })
          };
        }
      } catch (error) {
        console.error('删除会员码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '删除会员码失败: ' + error.message
          })
        };
      }
    }

    // 批量删除会员码
    if (routePath === '/member-codes/batch-delete' && method === 'POST') {
      try {
        const { codeIds } = body;
        console.log('批量删除会员码:', codeIds);

        let deletedCount = 0;

        console.log('🗑️ 批量删除会员码:', codeIds);

        // 逐个删除会员码
        for (const codeValue of codeIds) {
          const deleteResult = await db.collection('memberData').where({ code: codeValue }).remove();
          console.log(`删除 ${codeValue}:`, deleteResult.deleted, '个');
          if (deleteResult.deleted > 0) {
            deletedCount++;
          }
        }

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: `成功删除 ${deletedCount} 个会员码`
          })
        };
      } catch (error) {
        console.error('批量删除会员码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '批量删除会员码失败: ' + error.message
          })
        };
      }
    }

    // 默认404响应
    return createResponse(404, {
      success: false,
      message: 'API endpoint not found',
      path: routePath,
      method: method,
      originalPath: path
    });

  } catch (error) {
    console.error('Function error:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};
