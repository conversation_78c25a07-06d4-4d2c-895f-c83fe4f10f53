const axios = require('axios');

async function debugVIP123() {
  console.log('🔍 调试 VIP123 会员码问题...');
  
  try {
    // 1. 获取所有会员码，查看VIP123的详细信息
    console.log('\n1. 获取会员码列表，查看VIP123详情:');
    const listResponse = await axios.get('https://api.dznovel.top/api/admin/member-code/list?page=1&limit=50');
    
    if (listResponse.data.success) {
      const allCodes = listResponse.data.data.codes;
      const vip123 = allCodes.find(code => code.code === 'VIP123');
      
      if (vip123) {
        console.log('   找到 VIP123:');
        console.log('   - 会员码:', vip123.code);
        console.log('   - 是否使用:', vip123.isUsed);
        console.log('   - 套餐ID:', vip123.packageId);
        console.log('   - 过期时间:', vip123.expireAt);
        console.log('   - 创建时间:', vip123.createdAt);
        console.log('   - 批次ID:', vip123.batchId);
        console.log('   - 数据库ID:', vip123._id);
        
        // 检查字符串长度和内容
        console.log('   - 会员码长度:', vip123.code.length);
        console.log('   - 会员码字符:', JSON.stringify(vip123.code));
        console.log('   - 是否包含特殊字符:', /[^A-Z0-9]/.test(vip123.code));
        
      } else {
        console.log('   ❌ 在列表中未找到 VIP123');
        console.log('   所有会员码:', allCodes.map(c => c.code));
      }
    }
    
    // 2. 测试验证API
    console.log('\n2. 测试验证 VIP123:');
    try {
      const validateResponse = await axios.post('https://api.dznovel.top/api/member-code/validate', {
        code: 'VIP123'
      });
      console.log('   ✅ 验证成功:', validateResponse.data);
    } catch (error) {
      if (error.response) {
        console.log('   ❌ 验证失败:', error.response.data);
        console.log('   状态码:', error.response.status);
      } else {
        console.log('   ❌ 网络错误:', error.message);
      }
    }
    
    // 3. 测试其他会员码作为对比
    console.log('\n3. 测试其他会员码作为对比:');
    const testCodes = ['VIP001', 'VIP002'];
    
    for (const testCode of testCodes) {
      try {
        const response = await axios.post('https://api.dznovel.top/api/member-code/validate', {
          code: testCode
        });
        console.log(`   ✅ ${testCode}: ${response.data.message}`);
      } catch (error) {
        if (error.response) {
          console.log(`   ❌ ${testCode}: ${error.response.data.message}`);
        } else {
          console.log(`   ❌ ${testCode}: 网络错误`);
        }
      }
    }
    
  } catch (error) {
    console.error('调试过程中发生错误:', error.message);
  }
}

debugVIP123();
