import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:fluwx/fluwx.dart'; // 暂时注释，Windows平台不支持
import '../models/order.dart';
import '../models/package.dart';
import '../models/user.dart';
import '../config/api_config.dart';
import 'auth_service.dart';

/// 支付服务
class PaymentService extends GetxService {
  late final Dio _dio;
  final AuthService _authService = Get.find<AuthService>();

  final RxList<MembershipPackage> packages = <MembershipPackage>[].obs;
  final RxList<Order> orders = <Order>[].obs;
  final RxBool isLoading = false.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    print('🔍 PaymentService - 开始初始化');
    _initializeDio();
    print('🔍 PaymentService - Dio初始化完成');
    await _initializeWechatPay();
    print('🔍 PaymentService - 微信支付初始化完成');
    await loadPackages();
    print('🔍 PaymentService - 套餐加载完成');
    await loadUserOrders();
    print('🔍 PaymentService - 订单加载完成');
  }

  /// 初始化Dio配置
  void _initializeDio() {
    _dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // 添加认证拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        final token = _authService.accessToken;
        print('🔍 PaymentService - 请求URL: ${options.uri}');
        print(
            '🔍 PaymentService - Token: ${token != null ? '${token.substring(0, 20)}...' : 'null'}');
        print('🔍 PaymentService - 用户登录状态: ${_authService.isLoggedIn.value}');

        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
          print('✅ PaymentService - 已添加认证头');
        } else {
          print('❌ PaymentService - 未找到Token');
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        print(
            '❌ PaymentService - 请求错误: ${error.response?.statusCode} - ${error.message}');
        if (error.response?.statusCode == 401) {
          // Token过期，提示用户重新登录
          Get.snackbar('认证失败', '登录已过期，请重新登录');
          await _authService.logout();
          Get.offAllNamed('/login');
        }
        handler.next(error);
      },
    ));
  }

  /// 初始化微信支付
  Future<void> _initializeWechatPay() async {
    try {
      // Windows平台暂不支持微信支付，移动平台时再启用
      // await registerWxApi(
      //   appId: "your_wechat_app_id", // 替换为实际的微信AppID
      //   doOnAndroid: true,
      //   doOnIOS: true,
      //   universalLink: "your_universal_link", // iOS需要
      // );
      print('微信支付初始化跳过（Windows平台）');
    } catch (e) {
      print('初始化微信支付失败: $e');
    }
  }

  /// 加载会员套餐
  Future<void> loadPackages() async {
    try {
      isLoading.value = true;
      final response = await _dio.get(ApiConfig.getEndpoint('packages'));

      if (response.data['success'] == true) {
        final packageList = (response.data['data'] as List)
            .map((json) => MembershipPackage.fromJson(json))
            .where((pkg) => pkg.isActive)
            .toList();

        packageList.sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
        packages.assignAll(packageList);
      }
    } catch (e) {
      print('加载套餐失败: $e');
      Get.snackbar('错误', '加载套餐失败: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  /// 加载用户订单
  Future<void> loadUserOrders() async {
    print('🔍 PaymentService - 开始加载用户订单');
    print('🔍 PaymentService - 用户登录状态: ${_authService.isLoggedIn.value}');
    print(
        '🔍 PaymentService - 当前用户: ${_authService.currentUser.value?.username ?? 'null'}');

    if (!_authService.isLoggedIn.value) {
      print('❌ PaymentService - 用户未登录，跳过加载订单');
      return;
    }

    try {
      print(
          '🔍 PaymentService - 发送订单请求到: ${ApiConfig.getEndpoint('myOrders')}');
      final response = await _dio.get(ApiConfig.getEndpoint('myOrders'));

      if (response.data['success'] == true) {
        final orderList = (response.data['data'] as List)
            .map((json) => Order.fromJson(json))
            .toList();

        orderList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        orders.assignAll(orderList);
        print('✅ PaymentService - 成功加载 ${orderList.length} 个订单');
      } else {
        print('❌ PaymentService - 服务器返回失败: ${response.data['message']}');
      }
    } catch (e) {
      print('❌ PaymentService - 加载订单失败: $e');
    }
  }

  /// 创建订单
  Future<Order?> createOrder(String packageId) async {
    if (!_authService.isLoggedIn.value) {
      Get.snackbar('错误', '请先登录');
      return null;
    }

    try {
      isLoading.value = true;
      final response = await _dio.post(
        ApiConfig.getEndpoint('createOrder'),
        data: {'packageId': packageId},
      );

      if (response.data['success'] == true) {
        final order = Order.fromJson(response.data['data']);
        orders.insert(0, order);
        return order;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '创建订单失败');
        return null;
      }
    } catch (e) {
      print('创建订单失败: $e');
      Get.snackbar('错误', '创建订单失败: ${e.toString()}');
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// 微信支付
  Future<bool> payWithWechat(String orderId) async {
    try {
      isLoading.value = true;

      // 获取微信支付参数
      final response = await _dio.post(
        ApiConfig.getEndpoint('wechatPay'),
        data: {'orderId': orderId},
      );

      if (response.data['success'] == true) {
        final paymentData = response.data['data'];

        // Windows平台暂不支持微信支付
        Get.snackbar('提示', 'Windows平台暂不支持微信支付，请在移动设备上使用');
        return false;

        // 移动平台时启用以下代码：
        // final result = await payWithWeChat(
        //   appId: paymentData['appId'],
        //   partnerId: paymentData['partnerId'],
        //   prepayId: paymentData['prepayId'],
        //   packageValue: paymentData['package'],
        //   nonceStr: paymentData['nonceStr'],
        //   timeStamp: paymentData['timeStamp'],
        //   sign: paymentData['sign'],
        // );
        //
        // if (result.isSuccessful) {
        //   await _checkPaymentStatus(orderId);
        //   return true;
        // } else {
        //   Get.snackbar('支付失败', result.errorMsg ?? '微信支付失败');
        //   return false;
        // }
      } else {
        Get.snackbar('错误', response.data['message'] ?? '获取支付参数失败');
        return false;
      }
    } catch (e) {
      print('微信支付失败: $e');
      Get.snackbar('错误', '微信支付失败: ${e.toString()}');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 会员码支付
  Future<bool> payWithMemberCode(String orderId, String memberCode) async {
    try {
      isLoading.value = true;

      final response = await _dio.post(
        ApiConfig.getEndpoint('memberCodePay'),
        data: {
          'orderId': orderId,
          'memberCode': memberCode,
        },
      );

      if (response.data['success'] == true) {
        // 会员码支付成功，更新订单状态
        await _updateOrderStatus(orderId, OrderStatus.paid);

        // 更新用户会员状态
        await _updateUserMembership(response.data['data']);

        Get.snackbar('成功', '会员码激活成功！');
        return true;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '会员码无效');
        return false;
      }
    } catch (e) {
      print('会员码支付失败: $e');
      Get.snackbar('错误', '会员码支付失败: ${e.toString()}');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 检查支付状态
  Future<void> _checkPaymentStatus(String orderId) async {
    try {
      final response = await _dio
          .get(ApiConfig.getEndpoint('paymentStatus', {'id': orderId}));

      if (response.data['success'] == true) {
        final orderData = response.data['data'];
        await _updateOrderStatus(
            orderId, OrderStatus.values.byName(orderData['status']));

        if (orderData['status'] == 'paid') {
          // 支付成功，更新用户会员状态
          await _updateUserMembership(orderData['membership']);
          Get.snackbar('成功', '支付成功！会员已激活');
        }
      }
    } catch (e) {
      print('检查支付状态失败: $e');
    }
  }

  /// 更新订单状态
  Future<void> _updateOrderStatus(String orderId, OrderStatus status) async {
    final orderIndex = orders.indexWhere((o) => o.id == orderId);
    if (orderIndex != -1) {
      orders[orderIndex].status = status;
      if (status == OrderStatus.paid) {
        orders[orderIndex].paidAt = DateTime.now();
      }
      orders.refresh();
    }
  }

  /// 更新用户会员状态
  Future<void> _updateUserMembership(
      Map<String, dynamic> membershipData) async {
    final currentUser = _authService.currentUser.value;
    if (currentUser != null) {
      currentUser.isMember = membershipData['isMember'] ?? true;
      currentUser.isPermanentMember = membershipData['isPermanent'] ?? false;

      if (membershipData['expireTime'] != null) {
        currentUser.memberExpireTime =
            DateTime.parse(membershipData['expireTime']);
      }

      if (membershipData['membershipType'] != null) {
        currentUser.membershipType =
            MembershipType.values.byName(membershipData['membershipType']);
      }

      _authService.currentUser.refresh();

      // 保存到本地
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_data', jsonEncode(currentUser.toJson()));
    }
  }

  /// 验证会员码
  Future<bool> validateMemberCode(String code) async {
    try {
      final response = await _dio.post(
        ApiConfig.getEndpoint('validateMemberCode'),
        data: {'code': code},
      );

      return response.data['success'] == true;
    } catch (e) {
      print('验证会员码失败: $e');
      return false;
    }
  }

  /// 使用会员码
  Future<Map<String, dynamic>?> useMemberCode(String code) async {
    try {
      print('🔍 PaymentService - 开始使用会员码: $code');

      // 先验证会员码是否有效
      final validateResponse = await _dio.post(
        ApiConfig.getEndpoint('validateMemberCode'),
        data: {'code': code},
      );

      print('🔍 PaymentService - 会员码验证响应: ${validateResponse.data}');

      if (validateResponse.data['success'] != true) {
        throw Exception(validateResponse.data['message'] ?? '会员码无效');
      }

      // 创建临时订单用于会员码支付
      final createOrderResponse = await _dio.post(
        ApiConfig.getEndpoint('createOrder'),
        data: {'packageId': 'member_code_package'}, // 临时套餐ID
      );

      print('🔍 PaymentService - 创建订单响应: ${createOrderResponse.data}');

      if (createOrderResponse.data['success'] != true) {
        throw Exception(createOrderResponse.data['message'] ?? '创建订单失败');
      }

      final orderId = createOrderResponse.data['data']['id'];

      // 使用会员码支付
      final response = await _dio.post(
        ApiConfig.getEndpoint('memberCodePay'),
        data: {
          'orderId': orderId,
          'memberCode': code,
        },
      );

      print('🔍 PaymentService - 会员码支付响应: ${response.data}');

      if (response.data['success'] == true) {
        // 刷新用户信息
        final authService = Get.find<AuthService>();
        authService.currentUser.refresh();

        return response.data;
      } else {
        throw Exception(response.data['message'] ?? '使用会员码失败');
      }
    } catch (e) {
      print('❌ PaymentService - 使用会员码失败: $e');
      rethrow;
    }
  }

  /// 取消订单
  Future<bool> cancelOrder(String orderId) async {
    try {
      final response = await _dio.post(
        ApiConfig.getEndpoint('cancelOrder', {'id': orderId}),
      );

      if (response.data['success'] == true) {
        await _updateOrderStatus(orderId, OrderStatus.cancelled);
        Get.snackbar('成功', '订单已取消');
        return true;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '取消订单失败');
        return false;
      }
    } catch (e) {
      print('取消订单失败: $e');
      Get.snackbar('错误', '取消订单失败: ${e.toString()}');
      return false;
    }
  }

  /// 获取用户当前会员限制
  MembershipLimits getCurrentLimits() {
    final user = _authService.currentUser.value;
    if (user == null || !user.isValidMember) {
      return FreeLimits.free;
    }

    // 根据用户会员类型返回对应限制
    // 这里可以从服务器获取或使用默认配置
    return MembershipLimits();
  }

  /// 检查功能是否可用
  bool canUseFeature(String feature) {
    final limits = getCurrentLimits();

    switch (feature) {
      case 'extended_features':
        return limits.canUseExtendedFeatures;
      case 'advanced_ai':
        return limits.canUseAdvancedAI;
      case 'multiple_formats':
        return limits.canExportToMultipleFormats;
      default:
        return true;
    }
  }

  /// 检查是否超出使用限制
  bool isWithinLimits(String limitType, int currentCount) {
    final limits = getCurrentLimits();

    switch (limitType) {
      case 'chapters_per_novel':
        // 对于非会员，检查每日章节限制而不是每部小说章节限制
        final user = _authService.currentUser.value;
        if (user == null || !user.isValidMember) {
          // 非会员：每日最多100章节
          return currentCount < 100;
        }
        return limits.maxChaptersPerNovel == -1 ||
            currentCount < limits.maxChaptersPerNovel;
      case 'knowledge_documents':
        return limits.maxKnowledgeDocuments == -1 ||
            currentCount < limits.maxKnowledgeDocuments;
      case 'novels_per_day':
        return limits.maxNovelsPerDay == -1 ||
            currentCount < limits.maxNovelsPerDay;
      case 'custom_character_types':
        return limits.maxCustomCharacterTypes == -1 ||
            currentCount < limits.maxCustomCharacterTypes;
      case 'data_sync':
        // 非会员不能使用数据同步
        final user = _authService.currentUser.value;
        return user != null && user.isValidMember;
      default:
        return true;
    }
  }
}
